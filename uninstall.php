<?php
/**
 * Uninstall script for AI Customer Segmentation
 *
 * This file is executed when the plugin is uninstalled (deleted).
 * It cleans up all plugin data from the database.
 *
 * @package AI_Customer_Segmentation
 */

// If uninstall not called from WordPress, then exit
if ( ! defined( 'WP_UNINSTALL_PLUGIN' ) ) {
    exit;
}

/**
 * Clean up plugin data on uninstall
 */
function ai_customer_segmentation_uninstall() {
    global $wpdb;

    // Remove database tables
    $table_name = $wpdb->prefix . 'ai_customer_segments';
    $wpdb->query( "DROP TABLE IF EXISTS {$table_name}" );

    // Remove plugin options
    $options_to_delete = array(
        'ai_cs_openai_api_key',
        'ai_cs_openai_model',
        'ai_cs_refresh_frequency',
        'ai_cs_enable_churn',
        'ai_cs_enable_next_order',
        'ai_cs_enable_campaigns',
        'ai_cs_rfm_quintiles',
    );

    foreach ( $options_to_delete as $option ) {
        delete_option( $option );
    }

    // Remove transients
    $transients_to_delete = array(
        'ai_cs_batch_processing',
        'ai_cs_openai_rate_limit',
        'ai_cs_last_campaigns',
        'ai_cs_dashboard_cache',
    );

    foreach ( $transients_to_delete as $transient ) {
        delete_transient( $transient );
    }

    // Remove user meta (if any were stored)
    $wpdb->query( "DELETE FROM {$wpdb->usermeta} WHERE meta_key LIKE 'ai_cs_%'" );

    // Clear scheduled events
    wp_clear_scheduled_hook( 'ai_customer_segmentation_daily_update' );
    wp_clear_scheduled_hook( 'ai_customer_segmentation_batch_process' );

    // Remove any cached data
    wp_cache_flush();
}

// Execute cleanup
ai_customer_segmentation_uninstall();
