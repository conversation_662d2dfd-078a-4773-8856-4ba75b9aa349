/**
 * Admin JavaScript for AI Customer Segmentation
 *
 * @package AI_Customer_Segmentation
 */

(function($) {
    'use strict';

    /**
     * Initialize when document is ready
     */
    $(document).ready(function() {
        initCharts();
        initActions();
        initTableFeatures();
    });

    /**
     * Initialize Chart.js charts
     */
    function initCharts() {
        if (typeof window.aiCustomerSegmentationData === 'undefined') {
            return;
        }

        var data = window.aiCustomerSegmentationData;

        // Set Chart.js global defaults to prevent infinite expansion
        if (typeof Chart !== 'undefined') {
            Chart.defaults.responsive = true;
            Chart.defaults.maintainAspectRatio = false;
            Chart.defaults.aspectRatio = 2;
        }

        // Segment Distribution Pie Chart
        var segmentCtx = document.getElementById('segmentDistributionChart');
        if (segmentCtx) {
            // Set explicit canvas size to prevent infinite expansion
            segmentCtx.style.height = '300px';
            segmentCtx.style.maxHeight = '300px';

            new Chart(segmentCtx, {
                type: 'pie',
                data: data.segmentDistribution,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    aspectRatio: 1,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                usePointStyle: true
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    var label = context.label || '';
                                    var value = context.parsed;
                                    var total = context.dataset.data.reduce(function(a, b) { return a + b; }, 0);
                                    var percentage = Math.round((value / total) * 100);
                                    return label + ': ' + value + ' (' + percentage + '%)';
                                }
                            }
                        }
                    },
                    layout: {
                        padding: {
                            top: 10,
                            bottom: 10
                        }
                    }
                }
            });
        }

        // Revenue by Segment Bar Chart
        var revenueCtx = document.getElementById('revenueBySegmentChart');
        if (revenueCtx) {
            // Set explicit canvas size to prevent infinite expansion
            revenueCtx.style.height = '300px';
            revenueCtx.style.maxHeight = '300px';

            new Chart(revenueCtx, {
                type: 'bar',
                data: data.revenueBySegment,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    aspectRatio: 2,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    return 'Revenue: $' + context.parsed.y.toLocaleString();
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return '$' + value.toLocaleString();
                                }
                            }
                        },
                        x: {
                            ticks: {
                                maxRotation: 45,
                                minRotation: 0
                            }
                        }
                    },
                    layout: {
                        padding: {
                            top: 10,
                            bottom: 10
                        }
                    }
                }
            });
        }

        // Churn Risk Doughnut Chart
        var churnCtx = document.getElementById('churnRiskChart');
        if (churnCtx) {
            // Set explicit canvas size to prevent infinite expansion
            churnCtx.style.height = '300px';
            churnCtx.style.maxHeight = '300px';

            new Chart(churnCtx, {
                type: 'doughnut',
                data: data.churnRisk,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    aspectRatio: 1,
                    plugins: {
                        legend: {
                            position: 'bottom',
                            labels: {
                                padding: 20,
                                usePointStyle: true
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    var label = context.label || '';
                                    var value = context.parsed;
                                    var total = context.dataset.data.reduce(function(a, b) { return a + b; }, 0);
                                    var percentage = Math.round((value / total) * 100);
                                    return label + ': ' + value + ' (' + percentage + '%)';
                                }
                            }
                        }
                    },
                    layout: {
                        padding: {
                            top: 10,
                            bottom: 10
                        }
                    }
                }
            });
        }
    }

    /**
     * Initialize action buttons
     */
    function initActions() {
        // Manual update button
        $('#ai-cs-manual-update').on('click', function() {
            var $button = $(this);
            var originalText = $button.text();
            
            $button.prop('disabled', true).text('Updating...');
            
            $.post(aiCustomerSegmentation.ajaxUrl, {
                action: 'ai_cs_manual_update',
                nonce: aiCustomerSegmentation.nonce
            }, function(response) {
                if (response.success) {
                    showNotice('success', response.data.message);
                    // Reload page after 3 seconds
                    setTimeout(function() {
                        location.reload();
                    }, 3000);
                } else {
                    showNotice('error', response.data.message || 'Update failed');
                    $button.prop('disabled', false).text(originalText);
                }
            }).fail(function() {
                showNotice('error', 'Request failed. Please try again.');
                $button.prop('disabled', false).text(originalText);
            });
        });

        // Test OpenAI button (dashboard)
        $('#ai-cs-test-openai').on('click', function() {
            var $button = $(this);
            var originalText = $button.text();

            $button.prop('disabled', true).text('Testing...');

            $.post(aiCustomerSegmentation.ajaxUrl, {
                action: 'ai_cs_test_openai',
                nonce: aiCustomerSegmentation.nonce
            }, function(response) {
                if (response.success) {
                    showNotice('success', response.data.message);
                } else {
                    showNotice('error', response.data.message || 'Test failed');
                }
                $button.prop('disabled', false).text(originalText);
            }).fail(function() {
                showNotice('error', 'Request failed. Please try again.');
                $button.prop('disabled', false).text(originalText);
            });
        });

        // Test OpenAI button (settings page)
        $('#test-openai-connection').on('click', function() {
            console.log('Test OpenAI button clicked');
            var $button = $(this);
            var $result = $('#test-result');
            var originalText = $button.text();

            $button.prop('disabled', true).text('Testing...');
            $result.html('');

            console.log('Making AJAX request to:', ajaxurl);
            console.log('Nonce:', aiCustomerSegmentation.nonce);

            $.post(ajaxurl, {
                action: 'ai_cs_test_openai',
                nonce: aiCustomerSegmentation.nonce
            }, function(response) {
                console.log('AJAX response:', response);
                $button.prop('disabled', false).text(originalText);

                if (response.success) {
                    $result.html('<div class="notice notice-success inline"><p>' + response.data.message + '</p></div>');
                } else {
                    $result.html('<div class="notice notice-error inline"><p>' + (response.data.message || 'Test failed') + '</p></div>');
                }
            }).fail(function(xhr, status, error) {
                console.log('AJAX failed:', status, error);
                console.log('Response:', xhr.responseText);
                $button.prop('disabled', false).text(originalText);
                $result.html('<div class="notice notice-error inline"><p>Request failed. Please try again.</p></div>');
            });
        });

        // Debug data button
        $('#ai-cs-debug-data').on('click', function() {
            var $button = $(this);
            var originalText = $button.text();

            $button.prop('disabled', true).text('Getting Debug Data...');

            $.post(aiCustomerSegmentation.ajaxUrl, {
                action: 'ai_cs_debug_data',
                nonce: aiCustomerSegmentation.nonce
            }, function(response) {
                $button.prop('disabled', false).text(originalText);

                if (response.success) {
                    console.log('Debug Data:', response.data);
                    var debugInfo = response.data.data;
                    var message = 'Debug Info (WooCommerce CRUD):\n' +
                        'Total Orders: ' + debugInfo.total_orders + '\n' +
                        'Completed Orders: ' + debugInfo.completed_orders + '\n' +
                        'Customers with Orders: ' + debugInfo.unique_customers_with_orders + '\n' +
                        'Order Statuses: ' + JSON.stringify(debugInfo.order_statuses, null, 2);

                    alert(message);
                    showNotice('info', 'Debug data logged to console. Found ' + debugInfo.completed_orders + ' completed orders from ' + debugInfo.unique_customers_with_orders + ' customers.');
                } else {
                    showNotice('error', 'Failed to get debug data');
                }
            }).fail(function() {
                $button.prop('disabled', false).text(originalText);
                showNotice('error', 'Request failed. Please try again.');
            });
        });

        // Debug API key button
        $('#debug-api-key').on('click', function() {
            var $button = $(this);
            var originalText = $button.text();

            $button.prop('disabled', true).text('Debugging...');

            $.post(ajaxurl, {
                action: 'ai_cs_debug_api_key',
                nonce: aiCustomerSegmentation.nonce
            }, function(response) {
                $button.prop('disabled', false).text(originalText);

                if (response.success) {
                    console.log('API Key Debug Info:', response.data);
                    var debugInfo = response.data.data;
                    var message = 'API Key Debug Info:\n' +
                        'Stored Key Exists: ' + debugInfo.stored_key_exists + '\n' +
                        'Stored Key Length: ' + debugInfo.stored_key_length + '\n' +
                        'Decrypted Key Exists: ' + debugInfo.decrypted_key_exists + '\n' +
                        'Decrypted Key Length: ' + debugInfo.decrypted_key_length + '\n' +
                        'Is Configured: ' + debugInfo.is_configured + '\n' +
                        'Starts with sk-: ' + debugInfo.starts_with_sk + '\n' +
                        'Stored Preview: ' + debugInfo.stored_key_preview + '\n' +
                        'Decrypted Preview: ' + debugInfo.decrypted_key_preview;

                    alert(message);
                    showNotice('info', 'API key debug info logged to console.');
                } else {
                    showNotice('error', 'Failed to get API key debug info');
                }
            }).fail(function() {
                $button.prop('disabled', false).text(originalText);
                showNotice('error', 'Request failed. Please try again.');
            });
        });

        // Test prompt button (settings page)
        $('#test-prompt').on('click', function() {
            var $button = $(this);
            var $result = $('#prompt-result');
            var originalText = $button.text();

            var testData = {
                rfm_score: $('#test_rfm_score').val(),
                total_spent: $('#test_total_spent').val(),
                total_orders: $('#test_total_orders').val(),
                days_ago: $('#test_days_ago').val()
            };

            $button.prop('disabled', true).text('Testing...');
            $result.html('');

            $.post(ajaxurl, {
                action: 'ai_cs_test_prompt',
                nonce: aiCustomerSegmentation.nonce,
                test_data: testData
            }, function(response) {
                $button.prop('disabled', false).text(originalText);

                if (response.success) {
                    $result.html('<div class="notice notice-success inline"><p><strong>Result:</strong> ' + response.data.result + '</p></div>');
                } else {
                    $result.html('<div class="notice notice-error inline"><p>' + (response.data.message || 'Test failed') + '</p></div>');
                }
            }).fail(function() {
                $button.prop('disabled', false).text(originalText);
                $result.html('<div class="notice notice-error inline"><p>Request failed. Please try again.</p></div>');
            });
        });
    }

    /**
     * Initialize table features
     */
    function initTableFeatures() {
        // Add loading state to export button
        $('.ai-cs-table-actions a[href*="export=csv"]').on('click', function() {
            var $link = $(this);
            var originalText = $link.text();
            
            $link.text('Exporting...');
            
            // Reset text after 3 seconds
            setTimeout(function() {
                $link.text(originalText);
            }, 3000);
        });

        // Auto-submit filter form on select change
        $('.ai-cs-filters select').on('change', function() {
            $(this).closest('form').submit();
        });

        // Add confirmation to bulk actions
        $('input[name="action"], input[name="action2"]').on('change', function() {
            var action = $(this).val();
            if (action === 'delete' || action === 'bulk-delete') {
                $(this).closest('form').on('submit', function(e) {
                    if (!confirm('Are you sure you want to delete the selected items?')) {
                        e.preventDefault();
                    }
                });
            }
        });
    }

    /**
     * Show admin notice
     */
    function showNotice(type, message) {
        var noticeClass = 'notice-' + type;
        var $notice = $('<div class="notice ' + noticeClass + ' is-dismissible"><p>' + message + '</p></div>');
        
        $('.wrap h1').after($notice);
        
        // Auto-dismiss after 5 seconds
        setTimeout(function() {
            $notice.fadeOut(function() {
                $(this).remove();
            });
        }, 5000);
        
        // Add dismiss functionality
        $notice.on('click', '.notice-dismiss', function() {
            $notice.fadeOut(function() {
                $(this).remove();
            });
        });
    }

    /**
     * Format currency for display
     */
    function formatCurrency(amount) {
        return '$' + parseFloat(amount).toLocaleString('en-US', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
    }

    /**
     * Format percentage for display
     */
    function formatPercentage(value) {
        return Math.round(value * 100) + '%';
    }

    /**
     * Debounce function for search inputs
     */
    function debounce(func, wait, immediate) {
        var timeout;
        return function() {
            var context = this, args = arguments;
            var later = function() {
                timeout = null;
                if (!immediate) func.apply(context, args);
            };
            var callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func.apply(context, args);
        };
    }

    /**
     * Initialize search with debounce
     */
    function initSearch() {
        var searchInput = $('#search');
        if (searchInput.length) {
            var debouncedSubmit = debounce(function() {
                searchInput.closest('form').submit();
            }, 500);
            
            searchInput.on('input', debouncedSubmit);
        }
    }

    /**
     * Initialize tooltips
     */
    function initTooltips() {
        $('[data-tooltip]').each(function() {
            var $element = $(this);
            var title = $element.data('tooltip');
            
            $element.attr('title', title).tooltip({
                position: { my: 'center bottom-20', at: 'center top' },
                tooltipClass: 'ai-cs-tooltip'
            });
        });
    }

    /**
     * Initialize date range picker
     */
    function initDateRangePicker() {
        $('.ai-cs-date-range').each(function() {
            var $container = $(this);
            var $fromInput = $container.find('input[name$="_from"]');
            var $toInput = $container.find('input[name$="_to"]');
            
            // Ensure 'from' date is not after 'to' date
            $fromInput.on('change', function() {
                var fromDate = new Date($(this).val());
                var toDate = new Date($toInput.val());
                
                if (fromDate > toDate && $toInput.val()) {
                    $toInput.val($(this).val());
                }
                
                $toInput.attr('min', $(this).val());
            });
            
            // Ensure 'to' date is not before 'from' date
            $toInput.on('change', function() {
                var fromDate = new Date($fromInput.val());
                var toDate = new Date($(this).val());
                
                if (toDate < fromDate && $fromInput.val()) {
                    $fromInput.val($(this).val());
                }
                
                $fromInput.attr('max', $(this).val());
            });
        });
    }

    /**
     * Initialize all features
     */
    function initAllFeatures() {
        initSearch();
        initTooltips();
        initDateRangePicker();
    }

    // Initialize additional features
    initAllFeatures();

    /**
     * Handle chart resize issues
     */
    function handleChartResize() {
        // Prevent Chart.js from expanding infinitely
        var chartContainers = document.querySelectorAll('.ai-cs-chart-container canvas');
        chartContainers.forEach(function(canvas) {
            // Force height constraints
            canvas.style.maxHeight = '300px';
            canvas.style.height = '300px';

            // Prevent parent container from expanding
            var container = canvas.closest('.ai-cs-chart-container');
            if (container) {
                container.style.maxHeight = '400px';
                container.style.overflow = 'hidden';
            }
        });
    }

    // Apply chart resize handling after charts are initialized
    setTimeout(handleChartResize, 1000);

    // Handle window resize
    $(window).on('resize', function() {
        setTimeout(handleChartResize, 100);
    });

    /**
     * Refresh dashboard data
     */
    function refreshDashboard() {
        $.post(aiCustomerSegmentation.ajaxUrl, {
            action: 'ai_cs_refresh_dashboard',
            nonce: aiCustomerSegmentation.nonce
        }, function(response) {
            if (response.success) {
                location.reload();
            }
        });
    }

    /**
     * Export functionality
     */
    window.aiCsExportData = function(format, filters) {
        var params = {
            action: 'ai_cs_export_data',
            format: format,
            nonce: aiCustomerSegmentation.nonce
        };
        
        if (filters) {
            $.extend(params, filters);
        }
        
        var url = aiCustomerSegmentation.ajaxUrl + '?' + $.param(params);
        window.open(url, '_blank');
    };

    /**
     * Global functions for campaign page
     */
    window.aiCsCopyCampaigns = function() {
        // Implementation moved to campaigns.php inline script
    };

    window.aiCsExportCampaigns = function() {
        // Implementation moved to campaigns.php inline script
    };

})(jQuery);
