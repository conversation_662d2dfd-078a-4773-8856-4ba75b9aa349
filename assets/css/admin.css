/**
 * Admin CSS for AI Customer Segmentation
 *
 * @package AI_Customer_Segmentation
 */

/* ==========================================================================
   General Styles
   ========================================================================== */

.ai-cs-dashboard,
.ai-cs-table-view,
.ai-cs-settings,
.ai-cs-campaigns {
    max-width: 1200px;
    margin: 20px 0;
}

/* ==========================================================================
   Cards and Layout
   ========================================================================== */

.ai-cs-summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.ai-cs-card {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}

.ai-cs-card h3 {
    margin: 0 0 15px 0;
    font-size: 14px;
    font-weight: 600;
    color: #23282d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.ai-cs-metric {
    font-size: 32px;
    font-weight: 700;
    color: #0073aa;
    line-height: 1.2;
    margin-bottom: 5px;
}

.ai-cs-sub-metric {
    font-size: 13px;
    color: #646970;
}

/* ==========================================================================
   Badges and Status Indicators
   ========================================================================== */

.badge {
    display: inline-block;
    padding: 4px 8px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    border-radius: 3px;
    color: #fff;
    line-height: 1;
}

.badge-success { background-color: #46b450; }
.badge-primary { background-color: #0073aa; }
.badge-info { background-color: #00a0d2; }
.badge-warning { background-color: #ffb900; color: #23282d; }
.badge-danger { background-color: #dc3232; }
.badge-secondary { background-color: #646970; }

/* ==========================================================================
   Charts Section
   ========================================================================== */

.ai-cs-charts-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.ai-cs-chart-container {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    box-shadow: 0 1px 1px rgba(0, 0, 0, 0.04);
}

.ai-cs-chart-container h3 {
    margin: 0 0 20px 0;
    font-size: 16px;
    font-weight: 600;
    color: #23282d;
}

.ai-cs-chart-container canvas {
    max-width: 100%;
    height: auto;
}

/* ==========================================================================
   Actions Section
   ========================================================================== */

.ai-cs-actions-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.ai-cs-action-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-bottom: 15px;
}

.ai-cs-next-run {
    margin: 15px 0 0 0;
    font-size: 13px;
    color: #646970;
}

.ai-cs-activity-log ul {
    margin: 0;
    padding: 0;
    list-style: none;
}

.ai-cs-activity-log li {
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f1;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.ai-cs-activity-log li:last-child {
    border-bottom: none;
}

.ai-cs-timestamp {
    font-size: 12px;
    color: #646970;
}

/* ==========================================================================
   Top Segments
   ========================================================================== */

.ai-cs-top-segments {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.ai-cs-segment-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.ai-cs-segment-count {
    font-weight: 600;
    color: #23282d;
}

/* ==========================================================================
   Table View Styles
   ========================================================================== */

.ai-cs-filters {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.ai-cs-filter-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    align-items: end;
}

.ai-cs-filter-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #23282d;
}

.ai-cs-filter-group input,
.ai-cs-filter-group select {
    width: 100%;
}

.ai-cs-range-inputs,
.ai-cs-date-inputs {
    display: flex;
    align-items: center;
    gap: 8px;
}

.ai-cs-range-inputs input,
.ai-cs-date-inputs input {
    flex: 1;
}

.ai-cs-filter-actions {
    display: flex;
    gap: 10px;
}

.ai-cs-results-summary {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 10px 0;
}

.ai-cs-table-actions {
    display: flex;
    gap: 10px;
}

.ai-cs-table-container {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    overflow-x: auto;
}

.ai-cs-no-results {
    text-align: center;
    padding: 40px 20px;
    color: #646970;
    font-style: italic;
}

.ai-cs-churn-risk {
    font-weight: 600;
}

.ai-cs-na {
    color: #646970;
    font-style: italic;
}

.ai-cs-pagination {
    margin-top: 20px;
    text-align: center;
}

/* ==========================================================================
   Settings Styles
   ========================================================================== */

.ai-cs-settings-section {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.ai-cs-settings-section h3 {
    margin: 0 0 20px 0;
    padding-bottom: 10px;
    border-bottom: 1px solid #f0f0f1;
    font-size: 18px;
    font-weight: 600;
    color: #23282d;
}

.ai-cs-api-status {
    color: #46b450;
    font-weight: 600;
}

.ai-cs-api-status .dashicons {
    margin-right: 5px;
}

.ai-cs-test-result {
    margin-top: 10px;
}

.ai-cs-status-processing {
    color: #ffb900;
    font-weight: 600;
}

.ai-cs-status-idle {
    color: #46b450;
    font-weight: 600;
}

.ai-cs-status-processing .dashicons,
.ai-cs-status-idle .dashicons {
    margin-right: 5px;
}

/* ==========================================================================
   Campaign Styles
   ========================================================================== */

.ai-cs-segment-overview {
    margin-bottom: 30px;
}

.ai-cs-segment-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.ai-cs-segment-card {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 15px;
}

.ai-cs-segment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.ai-cs-segment-percentage {
    font-size: 18px;
    font-weight: 700;
    color: #0073aa;
}

.ai-cs-segment-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
}

.ai-cs-stat {
    text-align: center;
}

.ai-cs-stat-label {
    display: block;
    font-size: 11px;
    color: #646970;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 2px;
}

.ai-cs-stat-value {
    display: block;
    font-size: 14px;
    font-weight: 600;
    color: #23282d;
}

.ai-cs-campaign-generation {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 30px;
}

.ai-cs-campaign-results {
    margin-bottom: 30px;
}

.ai-cs-campaign-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 20px;
    margin-top: 15px;
}

.ai-cs-campaign-card {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
}

.ai-cs-campaign-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.ai-cs-campaign-name {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #23282d;
    flex: 1;
    margin-right: 10px;
}

.ai-cs-campaign-goal,
.ai-cs-campaign-action {
    margin-bottom: 15px;
    line-height: 1.5;
}

.ai-cs-campaign-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #f0f0f1;
}

.ai-cs-export-campaigns {
    background: #f8f9fa;
    border: 1px solid #e1e5e9;
    border-radius: 4px;
    padding: 20px;
    margin-top: 20px;
}

.ai-cs-export-buttons {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}

.ai-cs-integration-tips {
    margin-top: 30px;
}

.ai-cs-tips-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin-top: 15px;
}

.ai-cs-tip-card {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 15px;
}

.ai-cs-tip-card h4 {
    margin: 0 0 10px 0;
    font-size: 14px;
    font-weight: 600;
    color: #23282d;
}

.ai-cs-tip-card p {
    margin: 0;
    font-size: 13px;
    line-height: 1.5;
    color: #646970;
}

/* ==========================================================================
   Responsive Design
   ========================================================================== */

@media (max-width: 768px) {
    .ai-cs-summary-cards {
        grid-template-columns: 1fr;
    }
    
    .ai-cs-charts-section {
        grid-template-columns: 1fr;
    }
    
    .ai-cs-actions-section {
        grid-template-columns: 1fr;
    }
    
    .ai-cs-filter-row {
        grid-template-columns: 1fr;
    }
    
    .ai-cs-results-summary {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .ai-cs-segment-cards {
        grid-template-columns: 1fr;
    }
    
    .ai-cs-campaign-cards {
        grid-template-columns: 1fr;
    }
    
    .ai-cs-tips-grid {
        grid-template-columns: 1fr;
    }
    
    .ai-cs-segment-stats {
        grid-template-columns: 1fr;
        gap: 5px;
    }
    
    .ai-cs-campaign-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .ai-cs-campaign-name {
        margin-right: 0;
    }
}

/* ==========================================================================
   Utility Classes
   ========================================================================== */

.ai-cs-text-center { text-align: center; }
.ai-cs-text-right { text-align: right; }
.ai-cs-mb-0 { margin-bottom: 0; }
.ai-cs-mb-10 { margin-bottom: 10px; }
.ai-cs-mb-20 { margin-bottom: 20px; }
.ai-cs-mt-0 { margin-top: 0; }
.ai-cs-mt-10 { margin-top: 10px; }
.ai-cs-mt-20 { margin-top: 20px; }
