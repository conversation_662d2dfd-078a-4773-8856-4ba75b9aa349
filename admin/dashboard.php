<?php
/**
 * Dashboard Admin Page
 *
 * Displays customer segmentation dashboard with charts and statistics
 *
 * @package AI_Customer_Segmentation
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Dashboard class
 */
class AI_Customer_Segmentation_Dashboard {

    /**
     * Render dashboard page
     */
    public static function render() {
        $segment_manager = AI_Customer_Segmentation_Segment_Manager::get_instance();
        $cron_handler = AI_Customer_Segmentation_Cron_Handler::get_instance();

        // Get dashboard data
        $dashboard_stats = $segment_manager->get_dashboard_stats();
        $segment_distribution = $segment_manager->get_segment_distribution();
        $churn_stats = $segment_manager->get_churn_risk_stats();
        $processing_status = $cron_handler->get_processing_status();
        $next_run = $cron_handler->get_next_scheduled_run();

        ?>
        <div class="ai-cs-dashboard">
            
            <!-- Processing Status Alert -->
            <?php if ( $processing_status['is_processing'] ) : ?>
                <div class="notice notice-info">
                    <p>
                        <strong><?php esc_html_e( 'Processing in Progress', 'ai-customer-segmentation' ); ?></strong><br>
                        <?php echo esc_html( $processing_status['message'] ); ?>
                    </p>
                </div>
            <?php endif; ?>

            <!-- Summary Cards -->
            <div class="ai-cs-summary-cards">
                <div class="ai-cs-card">
                    <h3><?php esc_html_e( 'Total Customers', 'ai-customer-segmentation' ); ?></h3>
                    <div class="ai-cs-metric">
                        <?php echo esc_html( number_format( $dashboard_stats['total_customers'] ?? 0 ) ); ?>
                    </div>
                    <div class="ai-cs-sub-metric">
                        <?php 
                        $analyzed = $dashboard_stats['analyzed_customers'] ?? 0;
                        $total = $dashboard_stats['total_customers'] ?? 0;
                        $percentage = $total > 0 ? round( ( $analyzed / $total ) * 100, 1 ) : 0;
                        printf( 
                            esc_html__( '%s analyzed (%s%%)', 'ai-customer-segmentation' ),
                            number_format( $analyzed ),
                            $percentage
                        );
                        ?>
                    </div>
                </div>

                <div class="ai-cs-card">
                    <h3><?php esc_html_e( 'Average Customer Value', 'ai-customer-segmentation' ); ?></h3>
                    <div class="ai-cs-metric">
                        <?php echo ai_cs_format_currency( $dashboard_stats['avg_customer_value'] ?? 0 ); ?>
                    </div>
                    <div class="ai-cs-sub-metric">
                        <?php 
                        printf( 
                            esc_html__( 'Total Revenue: %s', 'ai-customer-segmentation' ),
                            ai_cs_format_currency( $dashboard_stats['total_revenue'] ?? 0 )
                        );
                        ?>
                    </div>
                </div>

                <div class="ai-cs-card">
                    <h3><?php esc_html_e( 'Average Churn Risk', 'ai-customer-segmentation' ); ?></h3>
                    <div class="ai-cs-metric">
                        <?php 
                        $avg_churn = $dashboard_stats['avg_churn_risk'] ?? 0;
                        echo esc_html( round( $avg_churn * 100, 1 ) . '%' );
                        ?>
                    </div>
                    <div class="ai-cs-sub-metric">
                        <?php 
                        $high_risk = $churn_stats['high_risk'] ?? 0;
                        printf( 
                            esc_html__( '%s customers at high risk', 'ai-customer-segmentation' ),
                            number_format( $high_risk )
                        );
                        ?>
                    </div>
                </div>

                <div class="ai-cs-card">
                    <h3><?php esc_html_e( 'Top Segments', 'ai-customer-segmentation' ); ?></h3>
                    <div class="ai-cs-top-segments">
                        <?php 
                        $top_segments = $dashboard_stats['top_segments'] ?? array();
                        foreach ( array_slice( $top_segments, 0, 3 ) as $segment ) :
                        ?>
                            <div class="ai-cs-segment-item">
                                <span class="ai-cs-segment-label badge badge-<?php echo esc_attr( ai_cs_get_segment_color( $segment['segment_label'] ) ); ?>">
                                    <?php echo esc_html( $segment['segment_label'] ); ?>
                                </span>
                                <span class="ai-cs-segment-count">
                                    <?php echo esc_html( number_format( $segment['count'] ) ); ?>
                                </span>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>

            <!-- Charts Section -->
            <div class="ai-cs-charts-section">
                <div class="ai-cs-chart-container">
                    <h3><?php esc_html_e( 'Customer Segments Distribution', 'ai-customer-segmentation' ); ?></h3>
                    <canvas id="segmentDistributionChart" width="400" height="200"></canvas>
                </div>

                <div class="ai-cs-chart-container">
                    <h3><?php esc_html_e( 'Revenue by Segment', 'ai-customer-segmentation' ); ?></h3>
                    <canvas id="revenueBySegmentChart" width="400" height="200"></canvas>
                </div>

                <div class="ai-cs-chart-container">
                    <h3><?php esc_html_e( 'Churn Risk Distribution', 'ai-customer-segmentation' ); ?></h3>
                    <canvas id="churnRiskChart" width="400" height="200"></canvas>
                </div>
            </div>

            <!-- Actions Section -->
            <div class="ai-cs-actions-section">
                <div class="ai-cs-card">
                    <h3><?php esc_html_e( 'Quick Actions', 'ai-customer-segmentation' ); ?></h3>
                    
                    <div class="ai-cs-action-buttons">
                        <button type="button" class="button button-primary" id="ai-cs-manual-update">
                            <?php esc_html_e( 'Update Segments Now', 'ai-customer-segmentation' ); ?>
                        </button>
                        
                        <button type="button" class="button" id="ai-cs-test-openai">
                            <?php esc_html_e( 'Test OpenAI Connection', 'ai-customer-segmentation' ); ?>
                        </button>
                        
                        <a href="<?php echo esc_url( add_query_arg( array( 'page' => 'ai-customer-segments', 'tab' => 'table-view' ), admin_url( 'admin.php' ) ) ); ?>" class="button">
                            <?php esc_html_e( 'View All Customers', 'ai-customer-segmentation' ); ?>
                        </a>
                    </div>

                    <?php if ( $next_run ) : ?>
                        <p class="ai-cs-next-run">
                            <?php 
                            printf( 
                                esc_html__( 'Next automatic update: %s', 'ai-customer-segmentation' ),
                                '<strong>' . esc_html( $next_run ) . '</strong>'
                            );
                            ?>
                        </p>
                    <?php endif; ?>
                </div>

                <div class="ai-cs-card">
                    <h3><?php esc_html_e( 'Recent Activity', 'ai-customer-segmentation' ); ?></h3>
                    
                    <?php if ( ! ai_cs_is_api_key_configured() ) : ?>
                        <div class="notice notice-warning inline">
                            <p>
                                <?php esc_html_e( 'OpenAI API key not configured.', 'ai-customer-segmentation' ); ?>
                                <a href="<?php echo esc_url( add_query_arg( array( 'page' => 'ai-customer-segments', 'tab' => 'settings' ), admin_url( 'admin.php' ) ) ); ?>">
                                    <?php esc_html_e( 'Configure now', 'ai-customer-segmentation' ); ?>
                                </a>
                            </p>
                        </div>
                    <?php else : ?>
                        <div class="ai-cs-activity-log">
                            <?php 
                            $recent_updates = self::get_recent_updates();
                            if ( empty( $recent_updates ) ) :
                            ?>
                                <p><?php esc_html_e( 'No recent activity.', 'ai-customer-segmentation' ); ?></p>
                            <?php else : ?>
                                <ul>
                                    <?php foreach ( $recent_updates as $update ) : ?>
                                        <li>
                                            <strong><?php echo esc_html( $update['action'] ); ?></strong>
                                            <span class="ai-cs-timestamp"><?php echo esc_html( $update['time'] ); ?></span>
                                        </li>
                                    <?php endforeach; ?>
                                </ul>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Chart Data for JavaScript -->
        <script type="text/javascript">
            window.aiCustomerSegmentationData = {
                segmentDistribution: <?php echo wp_json_encode( self::prepare_segment_chart_data( $segment_distribution ) ); ?>,
                revenueBySegment: <?php echo wp_json_encode( self::prepare_revenue_chart_data( $segment_distribution ) ); ?>,
                churnRisk: <?php echo wp_json_encode( self::prepare_churn_chart_data( $churn_stats ) ); ?>
            };
        </script>
        <?php
    }

    /**
     * Prepare segment distribution data for chart
     *
     * @param array $segment_distribution Segment distribution data
     * @return array Chart data
     */
    private static function prepare_segment_chart_data( $segment_distribution ) {
        $labels = array();
        $data = array();
        $colors = array();

        foreach ( $segment_distribution as $segment => $stats ) {
            $labels[] = $segment;
            $data[] = $stats['count'];
            $colors[] = self::get_chart_color( ai_cs_get_segment_color( $segment ) );
        }

        return array(
            'labels' => $labels,
            'datasets' => array(
                array(
                    'data' => $data,
                    'backgroundColor' => $colors,
                    'borderWidth' => 1,
                ),
            ),
        );
    }

    /**
     * Prepare revenue by segment data for chart
     *
     * @param array $segment_distribution Segment distribution data
     * @return array Chart data
     */
    private static function prepare_revenue_chart_data( $segment_distribution ) {
        $labels = array();
        $data = array();

        foreach ( $segment_distribution as $segment => $stats ) {
            $labels[] = $segment;
            $data[] = round( $stats['total_revenue'], 2 );
        }

        return array(
            'labels' => $labels,
            'datasets' => array(
                array(
                    'label' => __( 'Total Revenue', 'ai-customer-segmentation' ),
                    'data' => $data,
                    'backgroundColor' => 'rgba(54, 162, 235, 0.6)',
                    'borderColor' => 'rgba(54, 162, 235, 1)',
                    'borderWidth' => 1,
                ),
            ),
        );
    }

    /**
     * Prepare churn risk data for chart
     *
     * @param array $churn_stats Churn statistics
     * @return array Chart data
     */
    private static function prepare_churn_chart_data( $churn_stats ) {
        return array(
            'labels' => array(
                __( 'Low Risk', 'ai-customer-segmentation' ),
                __( 'Medium Risk', 'ai-customer-segmentation' ),
                __( 'High Risk', 'ai-customer-segmentation' ),
            ),
            'datasets' => array(
                array(
                    'data' => array(
                        $churn_stats['low_risk'] ?? 0,
                        $churn_stats['medium_risk'] ?? 0,
                        $churn_stats['high_risk'] ?? 0,
                    ),
                    'backgroundColor' => array(
                        '#28a745', // Green for low risk
                        '#ffc107', // Yellow for medium risk
                        '#dc3545', // Red for high risk
                    ),
                    'borderWidth' => 1,
                ),
            ),
        );
    }

    /**
     * Get chart color based on segment color class
     *
     * @param string $color_class Color class
     * @return string Hex color
     */
    private static function get_chart_color( $color_class ) {
        $colors = array(
            'success'   => '#28a745',
            'primary'   => '#007cba',
            'info'      => '#17a2b8',
            'warning'   => '#ffc107',
            'danger'    => '#dc3545',
            'secondary' => '#6c757d',
        );

        return $colors[ $color_class ] ?? '#6c757d';
    }

    /**
     * Get recent updates for activity log
     *
     * @return array Recent updates
     */
    private static function get_recent_updates() {
        // This would typically come from a log table or transient
        // For now, return sample data
        return array(
            array(
                'action' => __( 'Processed 150 customers', 'ai-customer-segmentation' ),
                'time'   => '2 hours ago',
            ),
            array(
                'action' => __( 'Updated RFM scores', 'ai-customer-segmentation' ),
                'time'   => '1 day ago',
            ),
        );
    }
}
