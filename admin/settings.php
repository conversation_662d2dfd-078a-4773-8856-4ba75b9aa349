<?php
/**
 * Settings Admin Page
 *
 * Handles plugin configuration and settings
 *
 * @package AI_Customer_Segmentation
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Settings class
 */
class AI_Customer_Segmentation_Settings {

    /**
     * Render settings page
     */
    public static function render() {
        // Handle form submission
        if ( isset( $_POST['submit'] ) && wp_verify_nonce( $_POST['_wpnonce'], 'ai_cs_settings' ) ) {
            self::save_settings();
        }

        // Get current settings
        $settings = ai_cs_get_settings();
        $api_key_masked = ai_cs_mask_api_key( ai_cs_decrypt_api_key( $settings['api_key'] ) );

        ?>
        <div class="ai-cs-settings">
            <form method="post" action="">
                <?php wp_nonce_field( 'ai_cs_settings' ); ?>

                <!-- OpenAI Configuration -->
                <div class="ai-cs-settings-section">
                    <h3><?php esc_html_e( 'OpenAI Configuration', 'ai-customer-segmentation' ); ?></h3>
                    
                    <table class="form-table">
                        <tr>
                            <th scope="row">
                                <label for="openai_api_key"><?php esc_html_e( 'OpenAI API Key', 'ai-customer-segmentation' ); ?></label>
                            </th>
                            <td>
                                <input type="password" id="openai_api_key" name="openai_api_key" value="<?php echo esc_attr( $api_key_masked ); ?>" class="regular-text" autocomplete="off">
                                <p class="description">
                                    <?php 
                                    printf(
                                        esc_html__( 'Enter your OpenAI API key. You can get one from %s.', 'ai-customer-segmentation' ),
                                        '<a href="https://platform.openai.com/api-keys" target="_blank">OpenAI Platform</a>'
                                    );
                                    ?>
                                </p>
                                <?php if ( ! empty( $settings['api_key'] ) ) : ?>
                                    <p class="ai-cs-api-status">
                                        <span class="dashicons dashicons-yes-alt"></span>
                                        <?php esc_html_e( 'API key is configured', 'ai-customer-segmentation' ); ?>
                                    </p>
                                <?php endif; ?>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row">
                                <label for="openai_model"><?php esc_html_e( 'OpenAI Model', 'ai-customer-segmentation' ); ?></label>
                            </th>
                            <td>
                                <select id="openai_model" name="openai_model">
                                    <option value="gpt-3.5-turbo" <?php selected( $settings['openai_model'], 'gpt-3.5-turbo' ); ?>>
                                        GPT-3.5 Turbo (<?php esc_html_e( 'Recommended', 'ai-customer-segmentation' ); ?>)
                                    </option>
                                    <option value="gpt-4" <?php selected( $settings['openai_model'], 'gpt-4' ); ?>>
                                        GPT-4 (<?php esc_html_e( 'More accurate, higher cost', 'ai-customer-segmentation' ); ?>)
                                    </option>
                                    <option value="gpt-4-turbo-preview" <?php selected( $settings['openai_model'], 'gpt-4-turbo-preview' ); ?>>
                                        GPT-4 Turbo Preview
                                    </option>
                                </select>
                                <p class="description">
                                    <?php esc_html_e( 'Choose the OpenAI model to use for customer analysis. GPT-3.5 Turbo is recommended for most use cases.', 'ai-customer-segmentation' ); ?>
                                </p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row">
                                <?php esc_html_e( 'Test Connection', 'ai-customer-segmentation' ); ?>
                            </th>
                            <td>
                                <button type="button" id="test-openai-connection" class="button">
                                    <?php esc_html_e( 'Test OpenAI Connection', 'ai-customer-segmentation' ); ?>
                                </button>
                                <button type="button" id="debug-api-key" class="button">
                                    <?php esc_html_e( 'Debug API Key', 'ai-customer-segmentation' ); ?>
                                </button>
                                <div id="test-result" class="ai-cs-test-result"></div>
                            </td>
                        </tr>
                    </table>
                </div>

                <!-- Update Frequency -->
                <div class="ai-cs-settings-section">
                    <h3><?php esc_html_e( 'Update Frequency', 'ai-customer-segmentation' ); ?></h3>
                    
                    <table class="form-table">
                        <tr>
                            <th scope="row">
                                <?php esc_html_e( 'Automatic Updates', 'ai-customer-segmentation' ); ?>
                            </th>
                            <td>
                                <fieldset>
                                    <label>
                                        <input type="radio" name="refresh_frequency" value="daily" <?php checked( $settings['refresh_frequency'], 'daily' ); ?>>
                                        <?php esc_html_e( 'Daily', 'ai-customer-segmentation' ); ?>
                                    </label><br>
                                    <label>
                                        <input type="radio" name="refresh_frequency" value="weekly" <?php checked( $settings['refresh_frequency'], 'weekly' ); ?>>
                                        <?php esc_html_e( 'Weekly', 'ai-customer-segmentation' ); ?>
                                    </label><br>
                                    <label>
                                        <input type="radio" name="refresh_frequency" value="manual" <?php checked( $settings['refresh_frequency'], 'manual' ); ?>>
                                        <?php esc_html_e( 'Manual only', 'ai-customer-segmentation' ); ?>
                                    </label>
                                </fieldset>
                                <p class="description">
                                    <?php esc_html_e( 'How often should customer segments be automatically updated?', 'ai-customer-segmentation' ); ?>
                                </p>
                            </td>
                        </tr>
                    </table>
                </div>

                <!-- Feature Toggles -->
                <div class="ai-cs-settings-section">
                    <h3><?php esc_html_e( 'Features', 'ai-customer-segmentation' ); ?></h3>
                    
                    <table class="form-table">
                        <tr>
                            <th scope="row">
                                <?php esc_html_e( 'AI Features', 'ai-customer-segmentation' ); ?>
                            </th>
                            <td>
                                <fieldset>
                                    <label>
                                        <input type="checkbox" name="enable_churn" value="1" <?php checked( $settings['enable_churn'] ); ?>>
                                        <?php esc_html_e( 'Enable churn prediction', 'ai-customer-segmentation' ); ?>
                                    </label><br>
                                    <label>
                                        <input type="checkbox" name="enable_next_order" value="1" <?php checked( $settings['enable_next_order'] ); ?>>
                                        <?php esc_html_e( 'Enable next order prediction', 'ai-customer-segmentation' ); ?>
                                    </label><br>
                                    <label>
                                        <input type="checkbox" name="enable_campaigns" value="1" <?php checked( $settings['enable_campaigns'] ); ?>>
                                        <?php esc_html_e( 'Enable campaign suggestions', 'ai-customer-segmentation' ); ?>
                                    </label>
                                </fieldset>
                                <p class="description">
                                    <?php esc_html_e( 'Enable or disable specific AI-powered features. Disabling features will reduce API usage.', 'ai-customer-segmentation' ); ?>
                                </p>
                            </td>
                        </tr>
                    </table>
                </div>

                <!-- Test Prompt -->
                <div class="ai-cs-settings-section">
                    <h3><?php esc_html_e( 'Test Prompt', 'ai-customer-segmentation' ); ?></h3>
                    
                    <table class="form-table">
                        <tr>
                            <th scope="row">
                                <label for="test_rfm_score"><?php esc_html_e( 'Test RFM Score', 'ai-customer-segmentation' ); ?></label>
                            </th>
                            <td>
                                <input type="text" id="test_rfm_score" name="test_rfm_score" value="543" class="small-text" maxlength="3" pattern="[1-5]{3}">
                                <input type="number" id="test_total_spent" name="test_total_spent" value="1250.00" step="0.01" min="0" class="small-text" placeholder="Total spent">
                                <input type="number" id="test_total_orders" name="test_total_orders" value="8" min="1" class="small-text" placeholder="Orders">
                                <input type="number" id="test_days_ago" name="test_days_ago" value="15" min="0" class="small-text" placeholder="Days ago">
                                <button type="button" id="test-prompt" class="button">
                                    <?php esc_html_e( 'Test Prompt', 'ai-customer-segmentation' ); ?>
                                </button>
                                <div id="prompt-result" class="ai-cs-test-result"></div>
                                <p class="description">
                                    <?php esc_html_e( 'Test how OpenAI will classify a customer with specific RFM data.', 'ai-customer-segmentation' ); ?>
                                </p>
                            </td>
                        </tr>
                    </table>
                </div>

                <!-- Current Status -->
                <div class="ai-cs-settings-section">
                    <h3><?php esc_html_e( 'Current Status', 'ai-customer-segmentation' ); ?></h3>
                    
                    <?php 
                    $cron_handler = AI_Customer_Segmentation_Cron_Handler::get_instance();
                    $next_run = $cron_handler->get_next_scheduled_run();
                    $processing_status = $cron_handler->get_processing_status();
                    ?>
                    
                    <table class="form-table">
                        <tr>
                            <th scope="row"><?php esc_html_e( 'Next Scheduled Update', 'ai-customer-segmentation' ); ?></th>
                            <td>
                                <?php if ( $next_run ) : ?>
                                    <strong><?php echo esc_html( $next_run ); ?></strong>
                                <?php else : ?>
                                    <em><?php esc_html_e( 'No automatic updates scheduled', 'ai-customer-segmentation' ); ?></em>
                                <?php endif; ?>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row"><?php esc_html_e( 'Processing Status', 'ai-customer-segmentation' ); ?></th>
                            <td>
                                <?php if ( $processing_status['is_processing'] ) : ?>
                                    <span class="ai-cs-status-processing">
                                        <span class="dashicons dashicons-update-alt"></span>
                                        <?php echo esc_html( $processing_status['message'] ); ?>
                                    </span>
                                <?php else : ?>
                                    <span class="ai-cs-status-idle">
                                        <span class="dashicons dashicons-yes-alt"></span>
                                        <?php esc_html_e( 'Idle', 'ai-customer-segmentation' ); ?>
                                    </span>
                                <?php endif; ?>
                            </td>
                        </tr>

                        <?php 
                        $segment_manager = AI_Customer_Segmentation_Segment_Manager::get_instance();
                        $dashboard_stats = $segment_manager->get_dashboard_stats();
                        ?>
                        
                        <tr>
                            <th scope="row"><?php esc_html_e( 'Total Customers', 'ai-customer-segmentation' ); ?></th>
                            <td>
                                <strong><?php echo esc_html( number_format( $dashboard_stats['total_customers'] ?? 0 ) ); ?></strong>
                                <?php if ( isset( $dashboard_stats['analyzed_customers'] ) ) : ?>
                                    <span class="description">
                                        (<?php echo esc_html( number_format( $dashboard_stats['analyzed_customers'] ) ); ?> analyzed)
                                    </span>
                                <?php endif; ?>
                            </td>
                        </tr>
                    </table>
                </div>

                <!-- Save Button -->
                <p class="submit">
                    <input type="submit" name="submit" id="submit" class="button-primary" value="<?php esc_attr_e( 'Save Settings', 'ai-customer-segmentation' ); ?>">
                </p>
            </form>
        </div>


        <?php
    }

    /**
     * Save settings
     */
    private static function save_settings() {
        // Verify nonce
        if ( ! wp_verify_nonce( $_POST['_wpnonce'], 'ai_cs_settings' ) ) {
            wp_die( 'Security check failed' );
        }

        // Verify permissions
        if ( ! current_user_can( 'manage_woocommerce' ) ) {
            wp_die( 'Insufficient permissions' );
        }

        // Save API key (encrypt if provided)
        if ( ! empty( $_POST['openai_api_key'] ) && strpos( $_POST['openai_api_key'], '*' ) === false ) {
            $api_key = ai_cs_sanitize_api_key( $_POST['openai_api_key'] );
            update_option( 'ai_cs_openai_api_key', ai_cs_encrypt_api_key( $api_key ) );
            ai_cs_log( 'API key saved and encrypted' );
        } else {
            ai_cs_log( 'API key not saved - either empty or contains masked characters' );
        }

        // Save other settings
        update_option( 'ai_cs_openai_model', sanitize_text_field( $_POST['openai_model'] ?? 'gpt-3.5-turbo' ) );
        update_option( 'ai_cs_refresh_frequency', sanitize_text_field( $_POST['refresh_frequency'] ?? 'daily' ) );
        update_option( 'ai_cs_enable_churn', ! empty( $_POST['enable_churn'] ) );
        update_option( 'ai_cs_enable_next_order', ! empty( $_POST['enable_next_order'] ) );
        update_option( 'ai_cs_enable_campaigns', ! empty( $_POST['enable_campaigns'] ) );

        // Update CRON schedule
        $cron_handler = AI_Customer_Segmentation_Cron_Handler::get_instance();
        $cron_handler->update_cron_schedule();

        // Show success message
        echo '<div class="notice notice-success is-dismissible"><p>' . esc_html__( 'Settings saved successfully.', 'ai-customer-segmentation' ) . '</p></div>';
    }
}
