<?php
/**
 * Campaign Ideas Admin Page
 *
 * Generates AI-powered marketing campaign suggestions
 *
 * @package AI_Customer_Segmentation
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Campaigns class
 */
class AI_Customer_Segmentation_Campaigns {

    /**
     * Render campaigns page
     */
    public static function render() {
        // Handle campaign generation request
        if ( isset( $_POST['generate_campaigns'] ) && wp_verify_nonce( $_POST['_wpnonce'], 'ai_cs_campaigns' ) ) {
            $campaigns = self::generate_campaigns();
        }

        $segment_manager = AI_Customer_Segmentation_Segment_Manager::get_instance();
        $segment_distribution = $segment_manager->get_segment_distribution();

        ?>
        <div class="ai-cs-campaigns">
            
            <!-- Current Segment Overview -->
            <div class="ai-cs-segment-overview">
                <h3><?php esc_html_e( 'Current Customer Segments', 'ai-customer-segmentation' ); ?></h3>
                
                <?php if ( empty( $segment_distribution ) ) : ?>
                    <div class="notice notice-warning">
                        <p>
                            <?php esc_html_e( 'No customer segments found. Please run the segmentation analysis first.', 'ai-customer-segmentation' ); ?>
                            <a href="<?php echo esc_url( add_query_arg( array( 'page' => 'ai-customer-segments', 'tab' => 'dashboard' ), admin_url( 'admin.php' ) ) ); ?>">
                                <?php esc_html_e( 'Go to Dashboard', 'ai-customer-segmentation' ); ?>
                            </a>
                        </p>
                    </div>
                <?php else : ?>
                    <div class="ai-cs-segment-cards">
                        <?php foreach ( $segment_distribution as $segment => $data ) : ?>
                            <div class="ai-cs-segment-card">
                                <div class="ai-cs-segment-header">
                                    <span class="ai-cs-segment-label badge badge-<?php echo esc_attr( ai_cs_get_segment_color( $segment ) ); ?>">
                                        <?php echo esc_html( $segment ); ?>
                                    </span>
                                    <span class="ai-cs-segment-percentage">
                                        <?php echo esc_html( $data['percentage'] . '%' ); ?>
                                    </span>
                                </div>
                                <div class="ai-cs-segment-stats">
                                    <div class="ai-cs-stat">
                                        <span class="ai-cs-stat-label"><?php esc_html_e( 'Customers', 'ai-customer-segmentation' ); ?></span>
                                        <span class="ai-cs-stat-value"><?php echo esc_html( number_format( $data['count'] ) ); ?></span>
                                    </div>
                                    <div class="ai-cs-stat">
                                        <span class="ai-cs-stat-label"><?php esc_html_e( 'Avg. Spent', 'ai-customer-segmentation' ); ?></span>
                                        <span class="ai-cs-stat-value"><?php echo ai_cs_format_currency( $data['avg_spent'] ); ?></span>
                                    </div>
                                    <div class="ai-cs-stat">
                                        <span class="ai-cs-stat-label"><?php esc_html_e( 'Total Revenue', 'ai-customer-segmentation' ); ?></span>
                                        <span class="ai-cs-stat-value"><?php echo ai_cs_format_currency( $data['total_revenue'] ); ?></span>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Campaign Generation -->
            <div class="ai-cs-campaign-generation">
                <h3><?php esc_html_e( 'Generate Campaign Ideas', 'ai-customer-segmentation' ); ?></h3>
                
                <?php if ( ! ai_cs_is_api_key_configured() ) : ?>
                    <div class="notice notice-error">
                        <p>
                            <?php esc_html_e( 'OpenAI API key is required to generate campaign suggestions.', 'ai-customer-segmentation' ); ?>
                            <a href="<?php echo esc_url( add_query_arg( array( 'page' => 'ai-customer-segments', 'tab' => 'settings' ), admin_url( 'admin.php' ) ) ); ?>">
                                <?php esc_html_e( 'Configure API Key', 'ai-customer-segmentation' ); ?>
                            </a>
                        </p>
                    </div>
                <?php elseif ( empty( $segment_distribution ) ) : ?>
                    <div class="notice notice-warning">
                        <p><?php esc_html_e( 'Customer segmentation data is required to generate campaigns.', 'ai-customer-segmentation' ); ?></p>
                    </div>
                <?php else : ?>
                    <form method="post" action="">
                        <?php wp_nonce_field( 'ai_cs_campaigns' ); ?>
                        
                        <p class="description">
                            <?php esc_html_e( 'Generate AI-powered marketing campaign suggestions based on your current customer segments.', 'ai-customer-segmentation' ); ?>
                        </p>
                        
                        <p class="submit">
                            <input type="submit" name="generate_campaigns" id="generate_campaigns" class="button-primary" value="<?php esc_attr_e( 'Generate Campaign Ideas', 'ai-customer-segmentation' ); ?>">
                        </p>
                    </form>
                <?php endif; ?>
            </div>

            <!-- Campaign Results -->
            <?php if ( isset( $campaigns ) ) : ?>
                <div class="ai-cs-campaign-results">
                    <h3><?php esc_html_e( 'AI-Generated Campaign Ideas', 'ai-customer-segmentation' ); ?></h3>
                    
                    <?php if ( empty( $campaigns ) ) : ?>
                        <div class="notice notice-error">
                            <p><?php esc_html_e( 'Failed to generate campaign ideas. Please check your OpenAI API configuration and try again.', 'ai-customer-segmentation' ); ?></p>
                        </div>
                    <?php else : ?>
                        <div class="ai-cs-campaign-cards">
                            <?php foreach ( $campaigns as $index => $campaign ) : ?>
                                <div class="ai-cs-campaign-card">
                                    <div class="ai-cs-campaign-header">
                                        <h4 class="ai-cs-campaign-name">
                                            <?php echo esc_html( $campaign['name'] ?? 'Campaign ' . ( $index + 1 ) ); ?>
                                        </h4>
                                        <?php if ( ! empty( $campaign['target'] ) ) : ?>
                                            <span class="ai-cs-campaign-target badge badge-<?php echo esc_attr( ai_cs_get_segment_color( $campaign['target'] ) ); ?>">
                                                <?php echo esc_html( $campaign['target'] ); ?>
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <?php if ( ! empty( $campaign['goal'] ) ) : ?>
                                        <div class="ai-cs-campaign-goal">
                                            <strong><?php esc_html_e( 'Goal:', 'ai-customer-segmentation' ); ?></strong>
                                            <?php echo esc_html( $campaign['goal'] ); ?>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php if ( ! empty( $campaign['action'] ) ) : ?>
                                        <div class="ai-cs-campaign-action">
                                            <strong><?php esc_html_e( 'Recommended Action:', 'ai-customer-segmentation' ); ?></strong>
                                            <?php echo esc_html( $campaign['action'] ); ?>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="ai-cs-campaign-actions">
                                        <?php if ( ! empty( $campaign['target'] ) ) : ?>
                                            <a href="<?php echo esc_url( add_query_arg( array( 'page' => 'ai-customer-segments', 'tab' => 'table-view', 'segment' => $campaign['target'] ), admin_url( 'admin.php' ) ) ); ?>" class="button">
                                                <?php esc_html_e( 'View Target Customers', 'ai-customer-segmentation' ); ?>
                                            </a>
                                        <?php endif; ?>
                                        
                                        <?php if ( self::is_woocommerce_email_available() ) : ?>
                                            <a href="<?php echo esc_url( admin_url( 'admin.php?page=wc-settings&tab=email' ) ); ?>" class="button">
                                                <?php esc_html_e( 'Setup Email Campaign', 'ai-customer-segmentation' ); ?>
                                            </a>
                                        <?php endif; ?>
                                        
                                        <?php if ( self::is_coupon_creation_available() ) : ?>
                                            <a href="<?php echo esc_url( admin_url( 'post-new.php?post_type=shop_coupon' ) ); ?>" class="button">
                                                <?php esc_html_e( 'Create Coupon', 'ai-customer-segmentation' ); ?>
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        
                        <!-- Export Options -->
                        <div class="ai-cs-export-campaigns">
                            <h4><?php esc_html_e( 'Export Campaign Ideas', 'ai-customer-segmentation' ); ?></h4>
                            <p class="description">
                                <?php esc_html_e( 'Save these campaign ideas for future reference or share with your marketing team.', 'ai-customer-segmentation' ); ?>
                            </p>
                            
                            <div class="ai-cs-export-buttons">
                                <button type="button" class="button" onclick="aiCsCopyCampaigns()">
                                    <?php esc_html_e( 'Copy to Clipboard', 'ai-customer-segmentation' ); ?>
                                </button>
                                <button type="button" class="button" onclick="aiCsExportCampaigns()">
                                    <?php esc_html_e( 'Export as Text', 'ai-customer-segmentation' ); ?>
                                </button>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>

            <!-- Integration Tips -->
            <div class="ai-cs-integration-tips">
                <h3><?php esc_html_e( 'Implementation Tips', 'ai-customer-segmentation' ); ?></h3>
                
                <div class="ai-cs-tips-grid">
                    <div class="ai-cs-tip-card">
                        <h4><?php esc_html_e( 'Email Marketing', 'ai-customer-segmentation' ); ?></h4>
                        <p><?php esc_html_e( 'Use WooCommerce email templates or plugins like MailChimp to create targeted email campaigns for each segment.', 'ai-customer-segmentation' ); ?></p>
                    </div>
                    
                    <div class="ai-cs-tip-card">
                        <h4><?php esc_html_e( 'Coupon Codes', 'ai-customer-segmentation' ); ?></h4>
                        <p><?php esc_html_e( 'Create segment-specific coupon codes with different discount percentages based on customer value.', 'ai-customer-segmentation' ); ?></p>
                    </div>
                    
                    <div class="ai-cs-tip-card">
                        <h4><?php esc_html_e( 'Product Recommendations', 'ai-customer-segmentation' ); ?></h4>
                        <p><?php esc_html_e( 'Show different product recommendations on your website based on customer segments.', 'ai-customer-segmentation' ); ?></p>
                    </div>
                    
                    <div class="ai-cs-tip-card">
                        <h4><?php esc_html_e( 'Retention Campaigns', 'ai-customer-segmentation' ); ?></h4>
                        <p><?php esc_html_e( 'Focus on high-risk customers with special offers and personalized communication.', 'ai-customer-segmentation' ); ?></p>
                    </div>
                </div>
            </div>
        </div>

        <script type="text/javascript">
        function aiCsCopyCampaigns() {
            var campaignText = '';
            document.querySelectorAll('.ai-cs-campaign-card').forEach(function(card, index) {
                var name = card.querySelector('.ai-cs-campaign-name').textContent;
                var target = card.querySelector('.ai-cs-campaign-target');
                var goal = card.querySelector('.ai-cs-campaign-goal');
                var action = card.querySelector('.ai-cs-campaign-action');
                
                campaignText += 'Campaign ' + (index + 1) + ': ' + name + '\n';
                if (target) campaignText += 'Target: ' + target.textContent + '\n';
                if (goal) campaignText += goal.textContent + '\n';
                if (action) campaignText += action.textContent + '\n';
                campaignText += '\n';
            });
            
            navigator.clipboard.writeText(campaignText).then(function() {
                alert('<?php esc_js_e( 'Campaign ideas copied to clipboard!', 'ai-customer-segmentation' ); ?>');
            });
        }

        function aiCsExportCampaigns() {
            var campaignText = 'AI-Generated Campaign Ideas\n';
            campaignText += '================================\n\n';
            
            document.querySelectorAll('.ai-cs-campaign-card').forEach(function(card, index) {
                var name = card.querySelector('.ai-cs-campaign-name').textContent;
                var target = card.querySelector('.ai-cs-campaign-target');
                var goal = card.querySelector('.ai-cs-campaign-goal');
                var action = card.querySelector('.ai-cs-campaign-action');
                
                campaignText += 'Campaign ' + (index + 1) + ': ' + name + '\n';
                campaignText += '--------------------------------\n';
                if (target) campaignText += 'Target Segment: ' + target.textContent + '\n';
                if (goal) campaignText += goal.textContent + '\n';
                if (action) campaignText += action.textContent + '\n';
                campaignText += '\n';
            });
            
            var blob = new Blob([campaignText], { type: 'text/plain' });
            var url = window.URL.createObjectURL(blob);
            var a = document.createElement('a');
            a.href = url;
            a.download = 'campaign-ideas-' + new Date().toISOString().split('T')[0] + '.txt';
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        }
        </script>
        <?php
    }

    /**
     * Generate campaign ideas using OpenAI
     *
     * @return array|false Campaign ideas or false on error
     */
    private static function generate_campaigns() {
        $segment_manager = AI_Customer_Segmentation_Segment_Manager::get_instance();
        $segment_distribution = $segment_manager->get_segment_distribution();

        if ( empty( $segment_distribution ) ) {
            return false;
        }

        $openai_client = AI_Customer_Segmentation_OpenAI_Client::get_instance();
        $campaigns = $openai_client->get_campaign_suggestions( $segment_distribution );

        // Cache the results for a short time
        if ( $campaigns ) {
            set_transient( 'ai_cs_last_campaigns', $campaigns, HOUR_IN_SECONDS );
        }

        return $campaigns;
    }

    /**
     * Check if WooCommerce email settings are available
     *
     * @return bool True if available
     */
    private static function is_woocommerce_email_available() {
        return class_exists( 'WC_Emails' );
    }

    /**
     * Check if coupon creation is available
     *
     * @return bool True if available
     */
    private static function is_coupon_creation_available() {
        return current_user_can( 'manage_woocommerce' ) && post_type_exists( 'shop_coupon' );
    }
}
