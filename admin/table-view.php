<?php
/**
 * Table View Admin Page
 *
 * Displays customer segments in a sortable, filterable table
 *
 * @package AI_Customer_Segmentation
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Table View class
 */
class AI_Customer_Segmentation_Table_View {

    /**
     * Render table view page
     */
    public static function render() {
        // Handle export request
        if ( isset( $_GET['export'] ) && $_GET['export'] === 'csv' ) {
            self::handle_csv_export();
            return;
        }

        // Get filter parameters
        $current_page = isset( $_GET['paged'] ) ? max( 1, intval( $_GET['paged'] ) ) : 1;
        $per_page = 20;
        $offset = ( $current_page - 1 ) * $per_page;

        $filters = array(
            'limit'      => $per_page,
            'offset'     => $offset,
            'orderby'    => sanitize_text_field( $_GET['orderby'] ?? 'updated_at' ),
            'order'      => sanitize_text_field( $_GET['order'] ?? 'DESC' ),
            'segment'    => sanitize_text_field( $_GET['segment'] ?? '' ),
            'churn_min'  => isset( $_GET['churn_min'] ) ? floatval( $_GET['churn_min'] ) : null,
            'churn_max'  => isset( $_GET['churn_max'] ) ? floatval( $_GET['churn_max'] ) : null,
            'date_from'  => sanitize_text_field( $_GET['date_from'] ?? '' ),
            'date_to'    => sanitize_text_field( $_GET['date_to'] ?? '' ),
            'search'     => sanitize_text_field( $_GET['search'] ?? '' ),
        );

        $segment_manager = AI_Customer_Segmentation_Segment_Manager::get_instance();
        $results = $segment_manager->get_customer_segments( $filters );
        $customers = $results['data'];
        $total_customers = $results['total'];

        // Get available segments for filter dropdown
        $segment_distribution = $segment_manager->get_segment_distribution();
        $available_segments = array_keys( $segment_distribution );

        ?>
        <div class="ai-cs-table-view">
            
            <!-- Filters Section -->
            <div class="ai-cs-filters">
                <form method="get" action="">
                    <input type="hidden" name="page" value="ai-customer-segments">
                    <input type="hidden" name="tab" value="table-view">
                    
                    <div class="ai-cs-filter-row">
                        <div class="ai-cs-filter-group">
                            <label for="search"><?php esc_html_e( 'Search', 'ai-customer-segmentation' ); ?></label>
                            <input type="text" id="search" name="search" value="<?php echo esc_attr( $filters['search'] ); ?>" placeholder="<?php esc_attr_e( 'Customer name or email', 'ai-customer-segmentation' ); ?>">
                        </div>

                        <div class="ai-cs-filter-group">
                            <label for="segment"><?php esc_html_e( 'Segment', 'ai-customer-segmentation' ); ?></label>
                            <select id="segment" name="segment">
                                <option value=""><?php esc_html_e( 'All Segments', 'ai-customer-segmentation' ); ?></option>
                                <?php foreach ( $available_segments as $segment ) : ?>
                                    <option value="<?php echo esc_attr( $segment ); ?>" <?php selected( $filters['segment'], $segment ); ?>>
                                        <?php echo esc_html( $segment ); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="ai-cs-filter-group">
                            <label for="churn_min"><?php esc_html_e( 'Churn Risk', 'ai-customer-segmentation' ); ?></label>
                            <div class="ai-cs-range-inputs">
                                <input type="number" id="churn_min" name="churn_min" value="<?php echo esc_attr( $filters['churn_min'] ); ?>" min="0" max="1" step="0.1" placeholder="Min">
                                <span>-</span>
                                <input type="number" id="churn_max" name="churn_max" value="<?php echo esc_attr( $filters['churn_max'] ); ?>" min="0" max="1" step="0.1" placeholder="Max">
                            </div>
                        </div>

                        <div class="ai-cs-filter-group">
                            <label for="date_from"><?php esc_html_e( 'Last Order Date', 'ai-customer-segmentation' ); ?></label>
                            <div class="ai-cs-date-inputs">
                                <input type="date" id="date_from" name="date_from" value="<?php echo esc_attr( $filters['date_from'] ); ?>">
                                <span>-</span>
                                <input type="date" id="date_to" name="date_to" value="<?php echo esc_attr( $filters['date_to'] ); ?>">
                            </div>
                        </div>

                        <div class="ai-cs-filter-actions">
                            <button type="submit" class="button"><?php esc_html_e( 'Filter', 'ai-customer-segmentation' ); ?></button>
                            <a href="<?php echo esc_url( add_query_arg( array( 'page' => 'ai-customer-segments', 'tab' => 'table-view' ), admin_url( 'admin.php' ) ) ); ?>" class="button">
                                <?php esc_html_e( 'Clear', 'ai-customer-segmentation' ); ?>
                            </a>
                        </div>
                    </div>
                </form>
            </div>

            <!-- Results Summary -->
            <div class="ai-cs-results-summary">
                <p>
                    <?php 
                    printf( 
                        esc_html__( 'Showing %1$s-%2$s of %3$s customers', 'ai-customer-segmentation' ),
                        number_format( $offset + 1 ),
                        number_format( min( $offset + $per_page, $total_customers ) ),
                        number_format( $total_customers )
                    );
                    ?>
                </p>
                
                <div class="ai-cs-table-actions">
                    <a href="<?php echo esc_url( add_query_arg( array_merge( $_GET, array( 'export' => 'csv' ) ) ) ); ?>" class="button">
                        <?php esc_html_e( 'Export CSV', 'ai-customer-segmentation' ); ?>
                    </a>
                </div>
            </div>

            <!-- Customer Table -->
            <div class="ai-cs-table-container">
                <table class="wp-list-table widefat fixed striped">
                    <thead>
                        <tr>
                            <?php 
                            $columns = array(
                                'customer_id'           => __( 'ID', 'ai-customer-segmentation' ),
                                'customer_name'         => __( 'Customer', 'ai-customer-segmentation' ),
                                'segment_label'         => __( 'Segment', 'ai-customer-segmentation' ),
                                'rfm_score'             => __( 'RFM Score', 'ai-customer-segmentation' ),
                                'total_spent'           => __( 'Total Spent', 'ai-customer-segmentation' ),
                                'total_orders'          => __( 'Orders', 'ai-customer-segmentation' ),
                                'churn_probability'     => __( 'Churn Risk', 'ai-customer-segmentation' ),
                                'last_order_date'       => __( 'Last Order', 'ai-customer-segmentation' ),
                                'next_order_prediction' => __( 'Next Order', 'ai-customer-segmentation' ),
                            );

                            foreach ( $columns as $column_key => $column_name ) :
                                $sortable = in_array( $column_key, array( 'customer_id', 'segment_label', 'total_spent', 'total_orders', 'churn_probability', 'last_order_date' ), true );
                                
                                if ( $sortable ) {
                                    $order_class = '';
                                    $order_link = add_query_arg( array_merge( $_GET, array( 'orderby' => $column_key, 'order' => 'ASC' ) ) );
                                    
                                    if ( $filters['orderby'] === $column_key ) {
                                        $order_class = 'sorted ' . strtolower( $filters['order'] );
                                        $order_link = add_query_arg( array_merge( $_GET, array( 'orderby' => $column_key, 'order' => $filters['order'] === 'ASC' ? 'DESC' : 'ASC' ) ) );
                                    }
                                    
                                    echo '<th class="sortable ' . esc_attr( $order_class ) . '">';
                                    echo '<a href="' . esc_url( $order_link ) . '">';
                                    echo esc_html( $column_name );
                                    echo '<span class="sorting-indicator"></span>';
                                    echo '</a>';
                                    echo '</th>';
                                } else {
                                    echo '<th>' . esc_html( $column_name ) . '</th>';
                                }
                            endforeach;
                            ?>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if ( empty( $customers ) ) : ?>
                            <tr>
                                <td colspan="<?php echo count( $columns ); ?>" class="ai-cs-no-results">
                                    <?php esc_html_e( 'No customers found matching your criteria.', 'ai-customer-segmentation' ); ?>
                                </td>
                            </tr>
                        <?php else : ?>
                            <?php foreach ( $customers as $customer_data ) : ?>
                                <?php 
                                $customer = new WC_Customer( $customer_data['customer_id'] );
                                $customer_name = trim( $customer->get_first_name() . ' ' . $customer->get_last_name() );
                                if ( empty( $customer_name ) ) {
                                    $customer_name = $customer->get_email();
                                }
                                ?>
                                <tr>
                                    <td><?php echo esc_html( $customer_data['customer_id'] ); ?></td>
                                    <td>
                                        <strong><?php echo esc_html( $customer_name ); ?></strong><br>
                                        <small><?php echo esc_html( $customer->get_email() ); ?></small>
                                    </td>
                                    <td>
                                        <span class="badge badge-<?php echo esc_attr( ai_cs_get_segment_color( $customer_data['segment_label'] ) ); ?>">
                                            <?php echo esc_html( $customer_data['segment_label'] ); ?>
                                        </span>
                                    </td>
                                    <td><?php echo esc_html( $customer_data['rfm_score'] ); ?></td>
                                    <td><?php echo ai_cs_format_currency( $customer_data['total_spent'] ); ?></td>
                                    <td><?php echo esc_html( number_format( $customer_data['total_orders'] ) ); ?></td>
                                    <td>
                                        <?php if ( $customer_data['churn_probability'] !== null ) : ?>
                                            <span class="ai-cs-churn-risk badge badge-<?php echo esc_attr( ai_cs_get_churn_risk_color( $customer_data['churn_probability'] ) ); ?>">
                                                <?php echo esc_html( round( $customer_data['churn_probability'] * 100, 1 ) . '%' ); ?>
                                            </span>
                                        <?php else : ?>
                                            <span class="ai-cs-na"><?php esc_html_e( 'N/A', 'ai-customer-segmentation' ); ?></span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php 
                                        if ( $customer_data['last_order_date'] ) {
                                            echo esc_html( date( 'M j, Y', strtotime( $customer_data['last_order_date'] ) ) );
                                        } else {
                                            echo '<span class="ai-cs-na">' . esc_html__( 'N/A', 'ai-customer-segmentation' ) . '</span>';
                                        }
                                        ?>
                                    </td>
                                    <td>
                                        <?php 
                                        if ( $customer_data['next_order_prediction'] ) {
                                            echo esc_html( date( 'M j, Y', strtotime( $customer_data['next_order_prediction'] ) ) );
                                        } else {
                                            echo '<span class="ai-cs-na">' . esc_html__( 'N/A', 'ai-customer-segmentation' ) . '</span>';
                                        }
                                        ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if ( $total_customers > $per_page ) : ?>
                <div class="ai-cs-pagination">
                    <?php
                    $total_pages = ceil( $total_customers / $per_page );
                    $pagination_args = array(
                        'base'      => add_query_arg( 'paged', '%#%' ),
                        'format'    => '',
                        'current'   => $current_page,
                        'total'     => $total_pages,
                        'prev_text' => '&laquo; ' . __( 'Previous', 'ai-customer-segmentation' ),
                        'next_text' => __( 'Next', 'ai-customer-segmentation' ) . ' &raquo;',
                    );
                    
                    echo paginate_links( $pagination_args );
                    ?>
                </div>
            <?php endif; ?>
        </div>
        <?php
    }

    /**
     * Handle CSV export
     */
    private static function handle_csv_export() {
        // Verify permissions
        if ( ! current_user_can( 'manage_woocommerce' ) ) {
            wp_die( 'Insufficient permissions' );
        }

        // Get filters from URL parameters
        $filters = array(
            'segment'    => sanitize_text_field( $_GET['segment'] ?? '' ),
            'churn_min'  => isset( $_GET['churn_min'] ) ? floatval( $_GET['churn_min'] ) : null,
            'churn_max'  => isset( $_GET['churn_max'] ) ? floatval( $_GET['churn_max'] ) : null,
            'date_from'  => sanitize_text_field( $_GET['date_from'] ?? '' ),
            'date_to'    => sanitize_text_field( $_GET['date_to'] ?? '' ),
            'search'     => sanitize_text_field( $_GET['search'] ?? '' ),
        );

        $segment_manager = AI_Customer_Segmentation_Segment_Manager::get_instance();
        $csv_content = $segment_manager->export_to_csv( $filters );

        if ( empty( $csv_content ) ) {
            wp_die( 'No data to export' );
        }

        // Set headers for CSV download
        $filename = 'customer-segments-' . date( 'Y-m-d-H-i-s' ) . '.csv';
        
        header( 'Content-Type: text/csv' );
        header( 'Content-Disposition: attachment; filename="' . $filename . '"' );
        header( 'Cache-Control: no-cache, must-revalidate' );
        header( 'Expires: 0' );

        echo $csv_content;
        exit;
    }
}
