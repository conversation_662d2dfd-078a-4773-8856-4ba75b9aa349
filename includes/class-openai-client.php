<?php
/**
 * OpenAI Client Class
 *
 * Handles communication with OpenAI API for customer segmentation
 *
 * @package AI_Customer_Segmentation
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * OpenAI Client class
 */
class AI_Customer_Segmentation_OpenAI_Client {

    /**
     * Single instance of the class
     *
     * @var AI_Customer_Segmentation_OpenAI_Client
     */
    private static $instance = null;

    /**
     * OpenAI API endpoint
     *
     * @var string
     */
    private $api_endpoint = 'https://api.openai.com/v1/chat/completions';

    /**
     * Rate limiting - requests per minute
     *
     * @var int
     */
    private $rate_limit = 60;

    /**
     * Get single instance
     *
     * @return AI_Customer_Segmentation_OpenAI_Client
     */
    public static function get_instance() {
        if ( null === self::$instance ) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        // Private constructor to prevent direct instantiation
    }

    /**
     * Get customer segment label from OpenAI
     *
     * @param array $customer_data Customer data including RFM scores
     * @return string|false Segment label or false on error
     */
    public function get_segment_label( $customer_data ) {
        if ( ! $this->is_api_configured() ) {
            return false;
        }

        $prompts = ai_cs_get_default_prompts();
        $prompt = $this->replace_prompt_variables( $prompts['segment'], $customer_data );

        $response = $this->make_api_request( $prompt );
        
        if ( ! $response ) {
            return false;
        }

        return $this->parse_segment_response( $response );
    }

    /**
     * Get churn probability from OpenAI
     *
     * @param array $customer_data Customer data including RFM scores
     * @return float|false Churn probability (0-1) or false on error
     */
    public function get_churn_probability( $customer_data ) {
        if ( ! $this->is_api_configured() ) {
            return false;
        }

        $settings = ai_cs_get_settings();
        if ( ! $settings['enable_churn'] ) {
            return false;
        }

        $prompts = ai_cs_get_default_prompts();
        $prompt = $this->replace_prompt_variables( $prompts['churn'], $customer_data );

        $response = $this->make_api_request( $prompt );
        
        if ( ! $response ) {
            return false;
        }

        return $this->parse_churn_response( $response );
    }

    /**
     * Get next order prediction from OpenAI
     *
     * @param array $customer_data Customer data including RFM scores
     * @return string|false Next order date (Y-m-d) or false on error
     */
    public function get_next_order_prediction( $customer_data ) {
        if ( ! $this->is_api_configured() ) {
            return false;
        }

        $settings = ai_cs_get_settings();
        if ( ! $settings['enable_next_order'] ) {
            return false;
        }

        $prompts = ai_cs_get_default_prompts();
        $prompt = $this->replace_prompt_variables( $prompts['next_order'], $customer_data );

        $response = $this->make_api_request( $prompt );
        
        if ( ! $response ) {
            return false;
        }

        return $this->parse_next_order_response( $response );
    }

    /**
     * Get campaign suggestions from OpenAI
     *
     * @param array $segment_distribution Segment distribution data
     * @return array|false Campaign suggestions or false on error
     */
    public function get_campaign_suggestions( $segment_distribution ) {
        if ( ! $this->is_api_configured() ) {
            return false;
        }

        $settings = ai_cs_get_settings();
        if ( ! $settings['enable_campaigns'] ) {
            return false;
        }

        // Build segment summary for prompt
        $segment_summary = array();
        foreach ( $segment_distribution as $segment => $data ) {
            $percentage = round( ( $data['count'] / $data['total'] ) * 100, 1 );
            $segment_summary[] = "{$percentage}% {$segment}";
        }

        $prompt = sprintf(
            'Based on these customer segments (%s), give me 3 WooCommerce-compatible marketing campaign ideas. For each campaign, provide: 1) Campaign name, 2) Target segment, 3) Goal, 4) Recommended action. Format as JSON array.',
            implode( ', ', $segment_summary )
        );

        $response = $this->make_api_request( $prompt );
        
        if ( ! $response ) {
            return false;
        }

        return $this->parse_campaign_response( $response );
    }

    /**
     * Make API request to OpenAI
     *
     * @param string $prompt The prompt to send
     * @return string|false API response or false on error
     */
    private function make_api_request( $prompt ) {
        // Check rate limiting
        if ( ! $this->check_rate_limit() ) {
            ai_cs_log( 'OpenAI API rate limit exceeded' );
            return false;
        }

        $api_key = $this->get_api_key();
        if ( ! $api_key ) {
            return false;
        }

        $settings = ai_cs_get_settings();
        $model = $settings['openai_model'] ?? 'gpt-3.5-turbo';

        $body = array(
            'model'       => $model,
            'messages'    => array(
                array(
                    'role'    => 'user',
                    'content' => $prompt,
                ),
            ),
            'max_tokens'  => 150,
            'temperature' => 0.3,
        );

        $args = array(
            'method'  => 'POST',
            'headers' => array(
                'Authorization' => 'Bearer ' . $api_key,
                'Content-Type'  => 'application/json',
            ),
            'body'    => wp_json_encode( $body ),
            'timeout' => 30,
        );

        $response = wp_remote_post( $this->api_endpoint, $args );

        if ( is_wp_error( $response ) ) {
            ai_cs_log( 'OpenAI API request failed', array( 'error' => $response->get_error_message() ) );
            return false;
        }

        $response_code = wp_remote_retrieve_response_code( $response );
        if ( $response_code !== 200 ) {
            ai_cs_log( 'OpenAI API returned error', array( 'code' => $response_code, 'body' => wp_remote_retrieve_body( $response ) ) );
            return false;
        }

        $body = wp_remote_retrieve_body( $response );
        $data = json_decode( $body, true );

        if ( ! isset( $data['choices'][0]['message']['content'] ) ) {
            ai_cs_log( 'Invalid OpenAI API response format', array( 'response' => $data ) );
            return false;
        }

        // Update rate limiting counter
        $this->update_rate_limit_counter();

        return trim( $data['choices'][0]['message']['content'] );
    }

    /**
     * Replace variables in prompt template
     *
     * @param string $prompt Prompt template
     * @param array  $customer_data Customer data
     * @return string Processed prompt
     */
    private function replace_prompt_variables( $prompt, $customer_data ) {
        $replacements = array(
            '{rfm_score}'    => $customer_data['rfm_score'] ?? 'N/A',
            '{total_spent}'  => number_format( $customer_data['total_spent'] ?? 0, 2 ),
            '{total_orders}' => $customer_data['total_orders'] ?? 0,
            '{days_ago}'     => $customer_data['days_since_last_order'] ?? 0,
        );

        return str_replace( array_keys( $replacements ), array_values( $replacements ), $prompt );
    }

    /**
     * Parse segment response from OpenAI
     *
     * @param string $response API response
     * @return string Parsed segment label
     */
    private function parse_segment_response( $response ) {
        // Clean up the response
        $segment = trim( $response );
        $segment = str_replace( array( '"', "'", '.', ',' ), '', $segment );

        // Validate against known segments
        $valid_segments = array(
            'VIP', 'Champions', 'Loyal Customers', 'Potential Loyalists',
            'New Customers', 'Promising', 'Need Attention', 'About to Sleep',
            'At Risk', 'Cannot Lose Them', 'Hibernating', 'Lost'
        );

        foreach ( $valid_segments as $valid_segment ) {
            if ( stripos( $segment, $valid_segment ) !== false ) {
                return $valid_segment;
            }
        }

        // Default fallback
        return 'Unclassified';
    }

    /**
     * Parse churn probability response from OpenAI
     *
     * @param string $response API response
     * @return float Churn probability (0-1)
     */
    private function parse_churn_response( $response ) {
        // Extract number from response
        preg_match( '/(\d*\.?\d+)/', $response, $matches );
        
        if ( empty( $matches[1] ) ) {
            return 0.5; // Default middle probability
        }

        $probability = floatval( $matches[1] );
        
        // Ensure it's between 0 and 1
        if ( $probability > 1 ) {
            $probability = $probability / 100; // Convert percentage to decimal
        }

        return max( 0, min( 1, $probability ) );
    }

    /**
     * Parse next order prediction response from OpenAI
     *
     * @param string $response API response
     * @return string|null Next order date (Y-m-d) or null
     */
    private function parse_next_order_response( $response ) {
        // Look for date pattern YYYY-MM-DD
        if ( preg_match( '/(\d{4}-\d{2}-\d{2})/', $response, $matches ) ) {
            $date = $matches[1];
            // Validate date
            if ( strtotime( $date ) !== false ) {
                return $date;
            }
        }

        return null;
    }

    /**
     * Parse campaign suggestions response from OpenAI
     *
     * @param string $response API response
     * @return array Campaign suggestions
     */
    private function parse_campaign_response( $response ) {
        // Try to parse as JSON first
        $campaigns = json_decode( $response, true );
        
        if ( json_last_error() === JSON_ERROR_NONE && is_array( $campaigns ) ) {
            return $campaigns;
        }

        // Fallback: parse as text
        $lines = explode( "\n", $response );
        $campaigns = array();
        $current_campaign = array();

        foreach ( $lines as $line ) {
            $line = trim( $line );
            if ( empty( $line ) ) {
                continue;
            }

            if ( preg_match( '/^\d+\.\s*(.+)/', $line, $matches ) ) {
                if ( ! empty( $current_campaign ) ) {
                    $campaigns[] = $current_campaign;
                }
                $current_campaign = array( 'name' => $matches[1] );
            } elseif ( stripos( $line, 'target:' ) === 0 ) {
                $current_campaign['target'] = trim( substr( $line, 7 ) );
            } elseif ( stripos( $line, 'goal:' ) === 0 ) {
                $current_campaign['goal'] = trim( substr( $line, 5 ) );
            } elseif ( stripos( $line, 'action:' ) === 0 ) {
                $current_campaign['action'] = trim( substr( $line, 7 ) );
            }
        }

        if ( ! empty( $current_campaign ) ) {
            $campaigns[] = $current_campaign;
        }

        return $campaigns;
    }

    /**
     * Check if API is configured
     *
     * @return bool True if API key is set
     */
    private function is_api_configured() {
        return ai_cs_is_api_key_configured();
    }

    /**
     * Get decrypted API key
     *
     * @return string|false API key or false if not set
     */
    private function get_api_key() {
        $encrypted_key = get_option( 'ai_cs_openai_api_key', '' );
        if ( empty( $encrypted_key ) ) {
            return false;
        }

        return ai_cs_decrypt_api_key( $encrypted_key );
    }

    /**
     * Check rate limiting
     *
     * @return bool True if request is allowed
     */
    private function check_rate_limit() {
        $current_minute = floor( time() / 60 );
        $rate_limit_data = get_transient( 'ai_cs_openai_rate_limit' );

        if ( ! $rate_limit_data || $rate_limit_data['minute'] !== $current_minute ) {
            return true;
        }

        return $rate_limit_data['count'] < $this->rate_limit;
    }

    /**
     * Update rate limiting counter
     */
    private function update_rate_limit_counter() {
        $current_minute = floor( time() / 60 );
        $rate_limit_data = get_transient( 'ai_cs_openai_rate_limit' );

        if ( ! $rate_limit_data || $rate_limit_data['minute'] !== $current_minute ) {
            $rate_limit_data = array(
                'minute' => $current_minute,
                'count'  => 1,
            );
        } else {
            $rate_limit_data['count']++;
        }

        set_transient( 'ai_cs_openai_rate_limit', $rate_limit_data, 120 );
    }

    /**
     * Test API connection
     *
     * @return array Test result
     */
    public function test_api_connection() {
        if ( ! $this->is_api_configured() ) {
            return array(
                'success' => false,
                'message' => __( 'API key not configured.', 'ai-customer-segmentation' ),
            );
        }

        $test_prompt = 'Respond with "API connection successful" if you can read this message.';
        $response = $this->make_api_request( $test_prompt );

        if ( $response === false ) {
            return array(
                'success' => false,
                'message' => __( 'Failed to connect to OpenAI API.', 'ai-customer-segmentation' ),
            );
        }

        return array(
            'success' => true,
            'message' => __( 'API connection successful.', 'ai-customer-segmentation' ),
            'response' => $response,
        );
    }
}
