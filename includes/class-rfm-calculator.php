<?php
/**
 * RFM Calculator Class
 *
 * Calculates Recency, Frequency, and Monetary scores for customers
 *
 * @package AI_Customer_Segmentation
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * RFM Calculator class
 */
class AI_Customer_Segmentation_RFM_Calculator {

    /**
     * Single instance of the class
     *
     * @var AI_Customer_Segmentation_RFM_Calculator
     */
    private static $instance = null;

    /**
     * Get single instance
     *
     * @return AI_Customer_Segmentation_RFM_Calculator
     */
    public static function get_instance() {
        if ( null === self::$instance ) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        // Private constructor to prevent direct instantiation
    }

    /**
     * Calculate RFM scores for all customers
     *
     * @param int $limit Limit number of customers to process (0 for all)
     * @return array Results summary
     */
    public function calculate_all_rfm_scores( $limit = 0 ) {
        $customers = $this->get_customers_with_orders( $limit );
        
        if ( empty( $customers ) ) {
            return array(
                'processed' => 0,
                'errors'    => 0,
                'message'   => __( 'No customers with orders found.', 'ai-customer-segmentation' ),
            );
        }

        // Calculate quintiles for all customers
        $quintiles = $this->calculate_rfm_quintiles( $customers );

        $processed = 0;
        $errors = 0;

        foreach ( $customers as $customer_data ) {
            try {
                $rfm_scores = $this->calculate_customer_rfm( $customer_data, $quintiles );
                $this->save_rfm_scores( $customer_data['customer_id'], $rfm_scores, $customer_data );
                $processed++;
            } catch ( Exception $e ) {
                ai_cs_log( 'Error calculating RFM for customer ' . $customer_data['customer_id'], array( 'error' => $e->getMessage() ) );
                $errors++;
            }
        }

        return array(
            'processed' => $processed,
            'errors'    => $errors,
            'message'   => sprintf(
                __( 'Processed %d customers with %d errors.', 'ai-customer-segmentation' ),
                $processed,
                $errors
            ),
        );
    }

    /**
     * Calculate RFM scores for a single customer
     *
     * @param int $customer_id Customer ID
     * @return array|false RFM scores or false on error
     */
    public function calculate_single_customer_rfm( $customer_id ) {
        $customer_data = ai_cs_get_customer_data( $customer_id );
        
        if ( ! $customer_data ) {
            return false;
        }

        // Get quintiles from existing data or calculate fresh
        $quintiles = $this->get_cached_quintiles();
        if ( ! $quintiles ) {
            $all_customers = $this->get_customers_with_orders();
            $quintiles = $this->calculate_rfm_quintiles( $all_customers );
        }

        $rfm_scores = $this->calculate_customer_rfm( $customer_data, $quintiles );
        $this->save_rfm_scores( $customer_id, $rfm_scores, $customer_data );

        return $rfm_scores;
    }

    /**
     * Get customers with completed orders
     *
     * @param int $limit Limit number of customers
     * @return array Customer data
     */
    private function get_customers_with_orders( $limit = 0 ) {
        global $wpdb;

        $limit_clause = $limit > 0 ? "LIMIT {$limit}" : '';

        // Get customers who have completed orders - Fixed SQL query
        $sql = "
            SELECT DISTINCT pm.meta_value as customer_id
            FROM {$wpdb->posts} p
            INNER JOIN {$wpdb->postmeta} pm ON p.ID = pm.post_id
            WHERE p.post_type = 'shop_order'
            AND p.post_status IN ('wc-completed', 'completed')
            AND pm.meta_key = '_customer_user'
            AND pm.meta_value > 0
            {$limit_clause}
        ";

        ai_cs_log( 'Getting customers with orders SQL: ' . $sql );
        $customer_ids = $wpdb->get_col( $sql );
        ai_cs_log( 'Found customer IDs: ' . wp_json_encode( $customer_ids ) );

        if ( empty( $customer_ids ) ) {
            // Try alternative approach using WooCommerce functions
            return $this->get_customers_with_orders_alternative( $limit );
        }

        $customers = array();
        foreach ( $customer_ids as $customer_id ) {
            $customer_data = ai_cs_get_customer_data( $customer_id );
            if ( $customer_data ) {
                $customers[] = $customer_data;
            }
        }

        ai_cs_log( 'Processed customers count: ' . count( $customers ) );
        return $customers;
    }

    /**
     * Alternative method to get customers using WooCommerce functions
     *
     * @param int $limit Limit number of customers
     * @return array Customer data
     */
    private function get_customers_with_orders_alternative( $limit = 0 ) {
        ai_cs_log( 'Using alternative method to get customers' );

        // Use WooCommerce order functions
        $args = array(
            'status' => array( 'completed' ),
            'limit'  => $limit > 0 ? $limit : -1,
            'return' => 'ids',
        );

        $order_ids = wc_get_orders( $args );
        ai_cs_log( 'Found order IDs: ' . wp_json_encode( array_slice( $order_ids, 0, 10 ) ) );

        $customer_ids = array();
        foreach ( $order_ids as $order_id ) {
            $order = wc_get_order( $order_id );
            if ( $order && $order->get_customer_id() > 0 ) {
                $customer_ids[] = $order->get_customer_id();
            }
        }

        // Remove duplicates
        $customer_ids = array_unique( $customer_ids );
        ai_cs_log( 'Unique customer IDs: ' . wp_json_encode( array_slice( $customer_ids, 0, 10 ) ) );

        $customers = array();
        foreach ( $customer_ids as $customer_id ) {
            $customer_data = ai_cs_get_customer_data( $customer_id );
            if ( $customer_data ) {
                $customers[] = $customer_data;
            }
        }

        ai_cs_log( 'Final customers count: ' . count( $customers ) );
        return $customers;
    }

    /**
     * Calculate RFM quintiles for all customers
     *
     * @param array $customers Customer data array
     * @return array Quintiles for R, F, M
     */
    private function calculate_rfm_quintiles( $customers ) {
        $recency_values = array();
        $frequency_values = array();
        $monetary_values = array();

        foreach ( $customers as $customer ) {
            $recency_values[] = $customer['days_since_last_order'];
            $frequency_values[] = $customer['total_orders'];
            $monetary_values[] = $customer['total_spent'];
        }

        $quintiles = array(
            'recency'   => ai_cs_calculate_quintiles( $recency_values ),
            'frequency' => ai_cs_calculate_quintiles( $frequency_values ),
            'monetary'  => ai_cs_calculate_quintiles( $monetary_values ),
        );

        // Cache quintiles for later use
        update_option( 'ai_cs_rfm_quintiles', $quintiles );

        return $quintiles;
    }

    /**
     * Get cached quintiles
     *
     * @return array|false Cached quintiles or false
     */
    private function get_cached_quintiles() {
        return get_option( 'ai_cs_rfm_quintiles', false );
    }

    /**
     * Calculate RFM scores for a single customer
     *
     * @param array $customer_data Customer data
     * @param array $quintiles RFM quintiles
     * @return array RFM scores
     */
    private function calculate_customer_rfm( $customer_data, $quintiles ) {
        // Calculate individual scores
        $recency_score = ai_cs_get_quintile_score(
            $customer_data['days_since_last_order'],
            $quintiles['recency'],
            true // Reverse for recency (lower days = higher score)
        );

        $frequency_score = ai_cs_get_quintile_score(
            $customer_data['total_orders'],
            $quintiles['frequency']
        );

        $monetary_score = ai_cs_get_quintile_score(
            $customer_data['total_spent'],
            $quintiles['monetary']
        );

        // Create RFM score string
        $rfm_score = $recency_score . $frequency_score . $monetary_score;

        return array(
            'rfm_score'       => $rfm_score,
            'recency_score'   => $recency_score,
            'frequency_score' => $frequency_score,
            'monetary_score'  => $monetary_score,
        );
    }

    /**
     * Save RFM scores to database
     *
     * @param int   $customer_id Customer ID
     * @param array $rfm_scores RFM scores
     * @param array $customer_data Customer data
     * @return bool Success
     */
    private function save_rfm_scores( $customer_id, $rfm_scores, $customer_data ) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'ai_customer_segments';

        $data = array(
            'customer_id'      => $customer_id,
            'rfm_score'        => $rfm_scores['rfm_score'],
            'recency_score'    => $rfm_scores['recency_score'],
            'frequency_score'  => $rfm_scores['frequency_score'],
            'monetary_score'   => $rfm_scores['monetary_score'],
            'total_orders'     => $customer_data['total_orders'],
            'total_spent'      => $customer_data['total_spent'],
            'last_order_date'  => date( 'Y-m-d H:i:s', $customer_data['last_order_date'] ),
            'updated_at'       => current_time( 'mysql' ),
        );

        // Check if record exists
        $existing = $wpdb->get_var( $wpdb->prepare(
            "SELECT id FROM {$table_name} WHERE customer_id = %d",
            $customer_id
        ) );

        if ( $existing ) {
            // Update existing record
            $result = $wpdb->update(
                $table_name,
                $data,
                array( 'customer_id' => $customer_id ),
                array( '%d', '%s', '%d', '%d', '%d', '%d', '%f', '%s', '%s' ),
                array( '%d' )
            );
        } else {
            // Insert new record
            $data['created_at'] = current_time( 'mysql' );
            $data['segment_label'] = 'Pending'; // Will be updated by OpenAI
            
            $result = $wpdb->insert(
                $table_name,
                $data,
                array( '%d', '%s', '%d', '%d', '%d', '%d', '%f', '%s', '%s', '%s', '%s' )
            );
        }

        return $result !== false;
    }

    /**
     * Get RFM statistics
     *
     * @return array RFM statistics
     */
    public function get_rfm_statistics() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'ai_customer_segments';

        $stats = $wpdb->get_row( "
            SELECT 
                COUNT(*) as total_customers,
                AVG(recency_score) as avg_recency,
                AVG(frequency_score) as avg_frequency,
                AVG(monetary_score) as avg_monetary,
                SUM(total_spent) as total_revenue,
                AVG(total_spent) as avg_spent_per_customer,
                MAX(last_order_date) as latest_order,
                MIN(last_order_date) as earliest_order
            FROM {$table_name}
        ", ARRAY_A );

        if ( ! $stats ) {
            return array();
        }

        // Add formatted values
        $stats['avg_recency'] = round( $stats['avg_recency'], 2 );
        $stats['avg_frequency'] = round( $stats['avg_frequency'], 2 );
        $stats['avg_monetary'] = round( $stats['avg_monetary'], 2 );
        $stats['avg_spent_per_customer'] = round( $stats['avg_spent_per_customer'], 2 );

        return $stats;
    }

    /**
     * Get customers needing RFM update
     *
     * @param int $days_threshold Days since last update
     * @return array Customer IDs needing update
     */
    public function get_customers_needing_update( $days_threshold = 1 ) {
        global $wpdb;

        $table_name = $wpdb->prefix . 'ai_customer_segments';
        $threshold_date = date( 'Y-m-d H:i:s', strtotime( "-{$days_threshold} days" ) );

        return $wpdb->get_col( $wpdb->prepare(
            "SELECT customer_id FROM {$table_name} WHERE updated_at < %s OR updated_at IS NULL",
            $threshold_date
        ) );
    }
}
