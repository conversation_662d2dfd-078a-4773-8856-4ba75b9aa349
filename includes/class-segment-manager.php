<?php
/**
 * Segment Manager Class
 *
 * Manages customer segments and database operations
 *
 * @package AI_Customer_Segmentation
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Segment Manager class
 */
class AI_Customer_Segmentation_Segment_Manager {

    /**
     * Single instance of the class
     *
     * @var AI_Customer_Segmentation_Segment_Manager
     */
    private static $instance = null;

    /**
     * Database table name
     *
     * @var string
     */
    private $table_name;

    /**
     * Get single instance
     *
     * @return AI_Customer_Segmentation_Segment_Manager
     */
    public static function get_instance() {
        if ( null === self::$instance ) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        global $wpdb;
        $this->table_name = $wpdb->prefix . 'ai_customer_segments';
    }

    /**
     * Update customer segment with AI predictions
     *
     * @param int   $customer_id Customer ID
     * @param array $ai_data AI predictions data
     * @return bool Success
     */
    public function update_customer_segment( $customer_id, $ai_data ) {
        global $wpdb;

        $update_data = array(
            'updated_at' => current_time( 'mysql' ),
        );

        $format = array( '%s' );

        // Add segment label if provided
        if ( ! empty( $ai_data['segment_label'] ) ) {
            $update_data['segment_label'] = sanitize_text_field( $ai_data['segment_label'] );
            $format[] = '%s';
        }

        // Add churn probability if provided
        if ( isset( $ai_data['churn_probability'] ) && is_numeric( $ai_data['churn_probability'] ) ) {
            $update_data['churn_probability'] = floatval( $ai_data['churn_probability'] );
            $format[] = '%f';
        }

        // Add next order prediction if provided
        if ( ! empty( $ai_data['next_order_prediction'] ) ) {
            $update_data['next_order_prediction'] = sanitize_text_field( $ai_data['next_order_prediction'] );
            $format[] = '%s';
        }

        $result = $wpdb->update(
            $this->table_name,
            $update_data,
            array( 'customer_id' => $customer_id ),
            $format,
            array( '%d' )
        );

        return $result !== false;
    }

    /**
     * Get customer segment data
     *
     * @param int $customer_id Customer ID
     * @return array|null Customer segment data
     */
    public function get_customer_segment( $customer_id ) {
        global $wpdb;

        return $wpdb->get_row( $wpdb->prepare(
            "SELECT * FROM {$this->table_name} WHERE customer_id = %d",
            $customer_id
        ), ARRAY_A );
    }

    /**
     * Get all customer segments with pagination
     *
     * @param array $args Query arguments
     * @return array Customer segments data
     */
    public function get_customer_segments( $args = array() ) {
        global $wpdb;

        $defaults = array(
            'limit'          => 20,
            'offset'         => 0,
            'orderby'        => 'updated_at',
            'order'          => 'DESC',
            'segment'        => '',
            'churn_min'      => null,
            'churn_max'      => null,
            'date_from'      => '',
            'date_to'        => '',
            'search'         => '',
        );

        $args = wp_parse_args( $args, $defaults );

        // Build WHERE clause
        $where_conditions = array( '1=1' );
        $where_values = array();

        // Filter by segment
        if ( ! empty( $args['segment'] ) ) {
            $where_conditions[] = 'segment_label = %s';
            $where_values[] = $args['segment'];
        }

        // Filter by churn probability range
        if ( is_numeric( $args['churn_min'] ) ) {
            $where_conditions[] = 'churn_probability >= %f';
            $where_values[] = floatval( $args['churn_min'] );
        }

        if ( is_numeric( $args['churn_max'] ) ) {
            $where_conditions[] = 'churn_probability <= %f';
            $where_values[] = floatval( $args['churn_max'] );
        }

        // Filter by date range
        if ( ! empty( $args['date_from'] ) ) {
            $where_conditions[] = 'last_order_date >= %s';
            $where_values[] = $args['date_from'];
        }

        if ( ! empty( $args['date_to'] ) ) {
            $where_conditions[] = 'last_order_date <= %s';
            $where_values[] = $args['date_to'];
        }

        // Search functionality (would need to join with users table for name search)
        if ( ! empty( $args['search'] ) ) {
            $where_conditions[] = 'customer_id IN (
                SELECT ID FROM ' . $wpdb->users . ' 
                WHERE display_name LIKE %s OR user_email LIKE %s
            )';
            $search_term = '%' . $wpdb->esc_like( $args['search'] ) . '%';
            $where_values[] = $search_term;
            $where_values[] = $search_term;
        }

        $where_clause = implode( ' AND ', $where_conditions );

        // Build ORDER BY clause
        $allowed_orderby = array( 'customer_id', 'segment_label', 'total_spent', 'total_orders', 'churn_probability', 'last_order_date', 'updated_at' );
        $orderby = in_array( $args['orderby'], $allowed_orderby, true ) ? $args['orderby'] : 'updated_at';
        $order = strtoupper( $args['order'] ) === 'ASC' ? 'ASC' : 'DESC';

        // Build LIMIT clause
        $limit_clause = '';
        if ( $args['limit'] > 0 ) {
            $limit_clause = $wpdb->prepare( 'LIMIT %d OFFSET %d', $args['limit'], $args['offset'] );
        }

        // Execute query
        $sql = "SELECT * FROM {$this->table_name} WHERE {$where_clause} ORDER BY {$orderby} {$order} {$limit_clause}";
        
        if ( ! empty( $where_values ) ) {
            $sql = $wpdb->prepare( $sql, $where_values );
        }

        $results = $wpdb->get_results( $sql, ARRAY_A );

        // Get total count for pagination
        $count_sql = "SELECT COUNT(*) FROM {$this->table_name} WHERE {$where_clause}";
        if ( ! empty( $where_values ) ) {
            $count_sql = $wpdb->prepare( $count_sql, $where_values );
        }
        $total_count = $wpdb->get_var( $count_sql );

        return array(
            'data'  => $results,
            'total' => intval( $total_count ),
        );
    }

    /**
     * Get segment distribution statistics
     *
     * @return array Segment distribution
     */
    public function get_segment_distribution() {
        global $wpdb;

        $results = $wpdb->get_results( "
            SELECT 
                segment_label,
                COUNT(*) as count,
                AVG(total_spent) as avg_spent,
                SUM(total_spent) as total_revenue,
                AVG(churn_probability) as avg_churn_risk
            FROM {$this->table_name}
            WHERE segment_label != 'Pending'
            GROUP BY segment_label
            ORDER BY count DESC
        ", ARRAY_A );

        $total_customers = $wpdb->get_var( "SELECT COUNT(*) FROM {$this->table_name} WHERE segment_label != 'Pending'" );

        $distribution = array();
        foreach ( $results as $row ) {
            $distribution[ $row['segment_label'] ] = array(
                'count'           => intval( $row['count'] ),
                'percentage'      => $total_customers > 0 ? round( ( $row['count'] / $total_customers ) * 100, 1 ) : 0,
                'avg_spent'       => round( floatval( $row['avg_spent'] ), 2 ),
                'total_revenue'   => round( floatval( $row['total_revenue'] ), 2 ),
                'avg_churn_risk'  => round( floatval( $row['avg_churn_risk'] ), 3 ),
                'total'           => intval( $total_customers ),
            );
        }

        return $distribution;
    }

    /**
     * Get churn risk statistics
     *
     * @return array Churn risk stats
     */
    public function get_churn_risk_stats() {
        global $wpdb;

        $stats = $wpdb->get_row( "
            SELECT 
                AVG(churn_probability) as avg_churn_risk,
                COUNT(CASE WHEN churn_probability >= 0.7 THEN 1 END) as high_risk,
                COUNT(CASE WHEN churn_probability >= 0.4 AND churn_probability < 0.7 THEN 1 END) as medium_risk,
                COUNT(CASE WHEN churn_probability < 0.4 THEN 1 END) as low_risk,
                COUNT(*) as total
            FROM {$this->table_name}
            WHERE churn_probability IS NOT NULL
        ", ARRAY_A );

        if ( ! $stats || $stats['total'] == 0 ) {
            return array(
                'avg_churn_risk' => 0,
                'high_risk'      => 0,
                'medium_risk'    => 0,
                'low_risk'       => 0,
                'total'          => 0,
            );
        }

        return array(
            'avg_churn_risk'      => round( floatval( $stats['avg_churn_risk'] ), 3 ),
            'high_risk'           => intval( $stats['high_risk'] ),
            'medium_risk'         => intval( $stats['medium_risk'] ),
            'low_risk'            => intval( $stats['low_risk'] ),
            'total'               => intval( $stats['total'] ),
            'high_risk_percent'   => round( ( $stats['high_risk'] / $stats['total'] ) * 100, 1 ),
            'medium_risk_percent' => round( ( $stats['medium_risk'] / $stats['total'] ) * 100, 1 ),
            'low_risk_percent'    => round( ( $stats['low_risk'] / $stats['total'] ) * 100, 1 ),
        );
    }

    /**
     * Get customers pending AI analysis
     *
     * @param int $limit Limit number of results
     * @return array Customer IDs pending analysis
     */
    public function get_customers_pending_analysis( $limit = 50 ) {
        global $wpdb;

        return $wpdb->get_col( $wpdb->prepare(
            "SELECT customer_id FROM {$this->table_name} 
             WHERE segment_label = 'Pending' OR segment_label = '' 
             ORDER BY updated_at ASC 
             LIMIT %d",
            $limit
        ) );
    }

    /**
     * Export customer segments to CSV
     *
     * @param array $args Query arguments
     * @return string CSV content
     */
    public function export_to_csv( $args = array() ) {
        $segments_data = $this->get_customer_segments( array_merge( $args, array( 'limit' => 0 ) ) );
        $segments = $segments_data['data'];

        if ( empty( $segments ) ) {
            return '';
        }

        // CSV headers
        $csv_headers = array(
            'Customer ID',
            'Email',
            'Name',
            'Segment',
            'RFM Score',
            'Total Orders',
            'Total Spent',
            'Churn Risk',
            'Last Order Date',
            'Next Order Prediction',
        );

        $csv_content = implode( ',', $csv_headers ) . "\n";

        // CSV data rows
        foreach ( $segments as $segment ) {
            $customer = new WC_Customer( $segment['customer_id'] );
            
            $row = array(
                $segment['customer_id'],
                '"' . $customer->get_email() . '"',
                '"' . $customer->get_first_name() . ' ' . $customer->get_last_name() . '"',
                '"' . $segment['segment_label'] . '"',
                $segment['rfm_score'],
                $segment['total_orders'],
                $segment['total_spent'],
                $segment['churn_probability'] ?? 'N/A',
                $segment['last_order_date'],
                $segment['next_order_prediction'] ?? 'N/A',
            );

            $csv_content .= implode( ',', $row ) . "\n";
        }

        return $csv_content;
    }

    /**
     * Delete customer segment data
     *
     * @param int $customer_id Customer ID
     * @return bool Success
     */
    public function delete_customer_segment( $customer_id ) {
        global $wpdb;

        return $wpdb->delete(
            $this->table_name,
            array( 'customer_id' => $customer_id ),
            array( '%d' )
        ) !== false;
    }

    /**
     * Clean up old or invalid segment data
     *
     * @param int $days_old Days to consider data old
     * @return int Number of records cleaned
     */
    public function cleanup_old_data( $days_old = 90 ) {
        global $wpdb;

        $cutoff_date = date( 'Y-m-d H:i:s', strtotime( "-{$days_old} days" ) );

        // Delete records for customers who no longer exist
        $deleted_customers = $wpdb->query( "
            DELETE cs FROM {$this->table_name} cs
            LEFT JOIN {$wpdb->users} u ON cs.customer_id = u.ID
            WHERE u.ID IS NULL
        " );

        // Delete very old records that haven't been updated
        $deleted_old = $wpdb->query( $wpdb->prepare(
            "DELETE FROM {$this->table_name} WHERE updated_at < %s",
            $cutoff_date
        ) );

        return $deleted_customers + $deleted_old;
    }

    /**
     * Get dashboard summary statistics
     *
     * @return array Dashboard stats
     */
    public function get_dashboard_stats() {
        global $wpdb;

        $stats = $wpdb->get_row( "
            SELECT 
                COUNT(*) as total_customers,
                COUNT(CASE WHEN segment_label != 'Pending' THEN 1 END) as analyzed_customers,
                AVG(total_spent) as avg_customer_value,
                SUM(total_spent) as total_revenue,
                AVG(churn_probability) as avg_churn_risk,
                MAX(last_order_date) as latest_order
            FROM {$this->table_name}
        ", ARRAY_A );

        if ( ! $stats ) {
            return array();
        }

        // Get top segments
        $top_segments = $wpdb->get_results( "
            SELECT segment_label, COUNT(*) as count
            FROM {$this->table_name}
            WHERE segment_label != 'Pending'
            GROUP BY segment_label
            ORDER BY count DESC
            LIMIT 3
        ", ARRAY_A );

        $stats['avg_customer_value'] = round( floatval( $stats['avg_customer_value'] ), 2 );
        $stats['total_revenue'] = round( floatval( $stats['total_revenue'] ), 2 );
        $stats['avg_churn_risk'] = round( floatval( $stats['avg_churn_risk'] ), 3 );
        $stats['top_segments'] = $top_segments;

        return $stats;
    }
}
