<?php
/**
 * CRON Handler Class
 *
 * Handles scheduled tasks for customer segmentation updates
 *
 * @package AI_Customer_Segmentation
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * CRON Handler class
 */
class AI_Customer_Segmentation_Cron_Handler {

    /**
     * Single instance of the class
     *
     * @var AI_Customer_Segmentation_Cron_Handler
     */
    private static $instance = null;

    /**
     * Batch size for processing customers
     *
     * @var int
     */
    private $batch_size = 10;

    /**
     * Get single instance
     *
     * @return AI_Customer_Segmentation_Cron_Handler
     */
    public static function get_instance() {
        if ( null === self::$instance ) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
    }

    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Register CRON hooks
        add_action( 'ai_customer_segmentation_daily_update', array( $this, 'daily_update_task' ) );
        add_action( 'ai_customer_segmentation_batch_process', array( $this, 'batch_process_task' ) );

        // Admin actions for manual triggers
        add_action( 'wp_ajax_ai_cs_manual_update', array( $this, 'handle_manual_update' ) );
    }

    /**
     * Daily update task
     * Main CRON job that runs daily to update customer segments
     */
    public function daily_update_task() {
        ai_cs_log( 'Starting daily customer segmentation update' );

        // Check if OpenAI API is configured
        if ( ! ai_cs_is_api_key_configured() ) {
            ai_cs_log( 'Daily update skipped: OpenAI API key not configured' );
            return;
        }

        // Step 1: Update RFM scores for all customers
        $rfm_calculator = AI_Customer_Segmentation_RFM_Calculator::get_instance();
        $rfm_results = $rfm_calculator->calculate_all_rfm_scores();

        ai_cs_log( 'RFM calculation completed', $rfm_results );

        // Step 2: Get customers pending AI analysis
        $segment_manager = AI_Customer_Segmentation_Segment_Manager::get_instance();
        $pending_customers = $segment_manager->get_customers_pending_analysis( $this->batch_size );

        if ( empty( $pending_customers ) ) {
            ai_cs_log( 'No customers pending AI analysis' );
            $this->cleanup_old_data();
            return;
        }

        // Step 3: Process customers in batches to avoid timeout and rate limits
        $this->schedule_batch_processing( $pending_customers );

        ai_cs_log( 'Scheduled batch processing for ' . count( $pending_customers ) . ' customers' );
    }

    /**
     * Batch process task
     * Processes a batch of customers for AI analysis
     */
    public function batch_process_task() {
        $batch_data = get_transient( 'ai_cs_batch_processing' );
        
        if ( ! $batch_data || empty( $batch_data['customers'] ) ) {
            ai_cs_log( 'No batch data found for processing' );
            return;
        }

        $customers_to_process = array_splice( $batch_data['customers'], 0, $this->batch_size );
        $processed_count = 0;
        $error_count = 0;

        $openai_client = AI_Customer_Segmentation_OpenAI_Client::get_instance();
        $segment_manager = AI_Customer_Segmentation_Segment_Manager::get_instance();

        foreach ( $customers_to_process as $customer_id ) {
            try {
                $this->process_single_customer( $customer_id, $openai_client, $segment_manager );
                $processed_count++;
                
                // Small delay to respect rate limits
                sleep( 1 );
                
            } catch ( Exception $e ) {
                ai_cs_log( 'Error processing customer ' . $customer_id, array( 'error' => $e->getMessage() ) );
                $error_count++;
            }
        }

        // Update batch data
        if ( ! empty( $batch_data['customers'] ) ) {
            // More customers to process, schedule next batch
            set_transient( 'ai_cs_batch_processing', $batch_data, HOUR_IN_SECONDS );
            wp_schedule_single_event( time() + 300, 'ai_customer_segmentation_batch_process' ); // 5 minutes delay
        } else {
            // All customers processed, cleanup
            delete_transient( 'ai_cs_batch_processing' );
            $this->cleanup_old_data();
        }

        ai_cs_log( 'Batch processing completed', array(
            'processed' => $processed_count,
            'errors'    => $error_count,
            'remaining' => count( $batch_data['customers'] ),
        ) );
    }

    /**
     * Process a single customer with AI analysis
     *
     * @param int $customer_id Customer ID
     * @param AI_Customer_Segmentation_OpenAI_Client $openai_client OpenAI client
     * @param AI_Customer_Segmentation_Segment_Manager $segment_manager Segment manager
     */
    private function process_single_customer( $customer_id, $openai_client, $segment_manager ) {
        // Get customer segment data
        $customer_segment = $segment_manager->get_customer_segment( $customer_id );
        
        if ( ! $customer_segment ) {
            throw new Exception( 'Customer segment data not found' );
        }

        $customer_data = array(
            'customer_id'           => $customer_id,
            'rfm_score'             => $customer_segment['rfm_score'],
            'total_spent'           => $customer_segment['total_spent'],
            'total_orders'          => $customer_segment['total_orders'],
            'days_since_last_order' => floor( ( time() - strtotime( $customer_segment['last_order_date'] ) ) / DAY_IN_SECONDS ),
        );

        $ai_data = array();

        // Get segment label
        $segment_label = $openai_client->get_segment_label( $customer_data );
        if ( $segment_label ) {
            $ai_data['segment_label'] = $segment_label;
        }

        // Get churn probability
        $churn_probability = $openai_client->get_churn_probability( $customer_data );
        if ( $churn_probability !== false ) {
            $ai_data['churn_probability'] = $churn_probability;
        }

        // Get next order prediction
        $next_order = $openai_client->get_next_order_prediction( $customer_data );
        if ( $next_order ) {
            $ai_data['next_order_prediction'] = $next_order;
        }

        // Update customer segment with AI data
        if ( ! empty( $ai_data ) ) {
            $segment_manager->update_customer_segment( $customer_id, $ai_data );
        }
    }

    /**
     * Schedule batch processing for customers
     *
     * @param array $customer_ids Customer IDs to process
     */
    private function schedule_batch_processing( $customer_ids ) {
        $batch_data = array(
            'customers'  => $customer_ids,
            'started_at' => time(),
        );

        set_transient( 'ai_cs_batch_processing', $batch_data, HOUR_IN_SECONDS );
        
        // Schedule immediate processing
        wp_schedule_single_event( time() + 60, 'ai_customer_segmentation_batch_process' );
    }

    /**
     * Cleanup old data
     */
    private function cleanup_old_data() {
        $segment_manager = AI_Customer_Segmentation_Segment_Manager::get_instance();
        $cleaned = $segment_manager->cleanup_old_data( 90 );
        
        if ( $cleaned > 0 ) {
            ai_cs_log( 'Cleaned up ' . $cleaned . ' old segment records' );
        }
    }

    /**
     * Handle manual update request from admin
     */
    public function handle_manual_update() {
        // Verify nonce and permissions
        if ( ! wp_verify_nonce( $_POST['nonce'] ?? '', 'ai_customer_segmentation_nonce' ) ) {
            wp_die( 'Security check failed' );
        }

        if ( ! current_user_can( 'manage_woocommerce' ) ) {
            wp_die( 'Insufficient permissions' );
        }

        // Trigger manual update
        $this->daily_update_task();

        wp_send_json_success( array(
            'message' => __( 'Manual update started. Check back in a few minutes for results.', 'ai-customer-segmentation' ),
        ) );
    }



    /**
     * Get processing status
     *
     * @return array Processing status
     */
    public function get_processing_status() {
        $batch_data = get_transient( 'ai_cs_batch_processing' );
        
        if ( ! $batch_data ) {
            return array(
                'is_processing' => false,
                'message'       => __( 'No processing in progress', 'ai-customer-segmentation' ),
            );
        }

        $remaining = count( $batch_data['customers'] );
        $elapsed = time() - $batch_data['started_at'];

        return array(
            'is_processing'     => true,
            'remaining'         => $remaining,
            'elapsed_minutes'   => round( $elapsed / 60, 1 ),
            'message'           => sprintf(
                __( 'Processing in progress. %d customers remaining.', 'ai-customer-segmentation' ),
                $remaining
            ),
        );
    }

    /**
     * Cancel current processing
     *
     * @return bool Success
     */
    public function cancel_processing() {
        delete_transient( 'ai_cs_batch_processing' );
        wp_clear_scheduled_hook( 'ai_customer_segmentation_batch_process' );
        
        return true;
    }

    /**
     * Update CRON schedule based on settings
     */
    public function update_cron_schedule() {
        $settings = ai_cs_get_settings();
        $frequency = $settings['refresh_frequency'];

        // Clear existing schedule
        wp_clear_scheduled_hook( 'ai_customer_segmentation_daily_update' );

        // Schedule based on frequency setting
        switch ( $frequency ) {
            case 'daily':
                wp_schedule_event( time(), 'daily', 'ai_customer_segmentation_daily_update' );
                break;
            case 'weekly':
                wp_schedule_event( time(), 'weekly', 'ai_customer_segmentation_daily_update' );
                break;
            case 'manual':
                // No automatic scheduling
                break;
        }
    }

    /**
     * Get next scheduled run time
     *
     * @return string|false Next run time or false if not scheduled
     */
    public function get_next_scheduled_run() {
        $timestamp = wp_next_scheduled( 'ai_customer_segmentation_daily_update' );
        
        if ( ! $timestamp ) {
            return false;
        }

        return date( 'Y-m-d H:i:s', $timestamp );
    }
}
