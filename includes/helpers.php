<?php
/**
 * Helper functions for AI Customer Segmentation
 *
 * @package AI_Customer_Segmentation
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

/**
 * Get customer data for RFM calculation
 *
 * @param int $customer_id Customer ID
 * @return array|false Customer data or false if not found
 */
function ai_cs_get_customer_data( $customer_id ) {
    if ( ! $customer_id ) {
        return false;
    }

    $customer = new WC_Customer( $customer_id );
    if ( ! $customer->get_id() ) {
        return false;
    }

    // Get customer orders - Fixed status to use correct WooCommerce status
    $orders = wc_get_orders( array(
        'customer_id' => $customer_id,
        'status'      => array( 'completed' ),
        'limit'       => -1,
    ) );

    ai_cs_log( 'Getting orders for customer ' . $customer_id . ': found ' . count( $orders ) . ' orders' );

    if ( empty( $orders ) ) {
        ai_cs_log( 'No completed orders found for customer ' . $customer_id );
        return false;
    }

    $total_spent = 0;
    $order_dates = array();
    $order_count = count( $orders );

    foreach ( $orders as $order ) {
        $total_spent += $order->get_total();
        $order_dates[] = $order->get_date_created()->getTimestamp();
    }

    // Get most recent order date
    $last_order_date = max( $order_dates );
    $days_since_last_order = floor( ( time() - $last_order_date ) / DAY_IN_SECONDS );

    return array(
        'customer_id'           => $customer_id,
        'email'                 => $customer->get_email(),
        'first_name'            => $customer->get_first_name(),
        'last_name'             => $customer->get_last_name(),
        'total_orders'          => $order_count,
        'total_spent'           => $total_spent,
        'last_order_date'       => $last_order_date,
        'days_since_last_order' => $days_since_last_order,
        'order_dates'           => $order_dates,
    );
}

/**
 * Calculate RFM quintiles for a dataset
 *
 * @param array $values Array of values to calculate quintiles for
 * @return array Quintile boundaries
 */
function ai_cs_calculate_quintiles( $values ) {
    if ( empty( $values ) ) {
        return array( 0, 0, 0, 0, 0 );
    }

    sort( $values );
    $count = count( $values );

    return array(
        $values[ floor( $count * 0.2 ) - 1 ] ?? 0,
        $values[ floor( $count * 0.4 ) - 1 ] ?? 0,
        $values[ floor( $count * 0.6 ) - 1 ] ?? 0,
        $values[ floor( $count * 0.8 ) - 1 ] ?? 0,
        $values[ $count - 1 ] ?? 0,
    );
}

/**
 * Get quintile score for a value
 *
 * @param float $value The value to score
 * @param array $quintiles Quintile boundaries
 * @param bool  $reverse Whether to reverse the scoring (for recency)
 * @return int Score from 1-5
 */
function ai_cs_get_quintile_score( $value, $quintiles, $reverse = false ) {
    $score = 1;
    
    foreach ( $quintiles as $index => $boundary ) {
        if ( $value > $boundary ) {
            $score = $index + 2;
        }
    }

    // Cap at 5
    $score = min( $score, 5 );

    // Reverse for recency (lower days = higher score)
    if ( $reverse ) {
        $score = 6 - $score;
    }

    return $score;
}

/**
 * Format currency for display
 *
 * @param float $amount Amount to format
 * @return string Formatted currency
 */
function ai_cs_format_currency( $amount ) {
    return wc_price( $amount );
}

/**
 * Get segment color for display
 *
 * @param string $segment Segment label
 * @return string CSS color class
 */
function ai_cs_get_segment_color( $segment ) {
    $colors = array(
        'VIP'              => 'success',
        'Champions'        => 'success',
        'Loyal Customers'  => 'primary',
        'Potential Loyalists' => 'info',
        'New Customers'    => 'info',
        'Promising'        => 'warning',
        'Need Attention'   => 'warning',
        'About to Sleep'   => 'danger',
        'At Risk'          => 'danger',
        'Cannot Lose Them' => 'danger',
        'Hibernating'      => 'secondary',
        'Lost'             => 'secondary',
    );

    return $colors[ $segment ] ?? 'secondary';
}

/**
 * Get churn risk level
 *
 * @param float $churn_probability Churn probability (0-1)
 * @return string Risk level
 */
function ai_cs_get_churn_risk_level( $churn_probability ) {
    if ( $churn_probability >= 0.7 ) {
        return __( 'High', 'ai-customer-segmentation' );
    } elseif ( $churn_probability >= 0.4 ) {
        return __( 'Medium', 'ai-customer-segmentation' );
    } else {
        return __( 'Low', 'ai-customer-segmentation' );
    }
}

/**
 * Get churn risk color class
 *
 * @param float $churn_probability Churn probability (0-1)
 * @return string CSS color class
 */
function ai_cs_get_churn_risk_color( $churn_probability ) {
    if ( $churn_probability >= 0.7 ) {
        return 'danger';
    } elseif ( $churn_probability >= 0.4 ) {
        return 'warning';
    } else {
        return 'success';
    }
}

/**
 * Sanitize OpenAI API key
 *
 * @param string $api_key API key to sanitize
 * @return string Sanitized API key
 */
function ai_cs_sanitize_api_key( $api_key ) {
    return sanitize_text_field( trim( $api_key ) );
}

/**
 * Encrypt API key for storage
 *
 * @param string $api_key API key to encrypt
 * @return string Encrypted API key
 */
function ai_cs_encrypt_api_key( $api_key ) {
    if ( empty( $api_key ) ) {
        return '';
    }

    // Simple base64 encoding (not secure, but better than plain text)
    // In production, use proper encryption
    return base64_encode( $api_key );
}

/**
 * Decrypt API key from storage
 *
 * @param string $encrypted_key Encrypted API key
 * @return string Decrypted API key
 */
function ai_cs_decrypt_api_key( $encrypted_key ) {
    if ( empty( $encrypted_key ) ) {
        return '';
    }

    return base64_decode( $encrypted_key );
}

/**
 * Mask API key for display
 *
 * @param string $api_key API key to mask
 * @return string Masked API key
 */
function ai_cs_mask_api_key( $api_key ) {
    if ( empty( $api_key ) ) {
        return '';
    }

    $length = strlen( $api_key );
    if ( $length <= 8 ) {
        return str_repeat( '*', $length );
    }

    return substr( $api_key, 0, 4 ) . str_repeat( '*', $length - 8 ) . substr( $api_key, -4 );
}

/**
 * Get default OpenAI prompts
 *
 * @return array Default prompts
 */
function ai_cs_get_default_prompts() {
    return array(
        'segment' => 'Given a customer with RFM score {rfm_score}, who spent ${total_spent} across {total_orders} orders and last ordered {days_ago} days ago, classify them into one of these segments: VIP, Champions, Loyal Customers, Potential Loyalists, New Customers, Promising, Need Attention, About to Sleep, At Risk, Cannot Lose Them, Hibernating, Lost. Return only the segment name.',
        'churn'   => 'Based on RFM score {rfm_score}, ${total_spent} total spend, {total_orders} orders, and {days_ago} days since last order, what is the probability (0.0 to 1.0) this customer will churn in the next 90 days? Return only the number.',
        'next_order' => 'Given RFM score {rfm_score}, ${total_spent} spent, {total_orders} orders, last order {days_ago} days ago, predict when they might order next. Return date in YYYY-MM-DD format or "unknown".',
    );
}

/**
 * Log debug information
 *
 * @param string $message Log message
 * @param array  $context Additional context
 */
function ai_cs_log( $message, $context = array() ) {
    if ( defined( 'WP_DEBUG' ) && WP_DEBUG ) {
        error_log( '[AI Customer Segmentation] ' . $message . ' ' . wp_json_encode( $context ) );
    }
}

/**
 * Check if OpenAI API key is configured
 *
 * @return bool True if API key is set
 */
function ai_cs_is_api_key_configured() {
    $api_key = get_option( 'ai_cs_openai_api_key', '' );
    ai_cs_log( 'Checking API key configuration: ' . ( ! empty( $api_key ) ? 'Key exists (length: ' . strlen( $api_key ) . ')' : 'No key found' ) );
    return ! empty( $api_key );
}

/**
 * Get plugin settings with defaults
 *
 * @return array Plugin settings
 */
function ai_cs_get_settings() {
    return array(
        'api_key'           => get_option( 'ai_cs_openai_api_key', '' ),
        'refresh_frequency' => get_option( 'ai_cs_refresh_frequency', 'daily' ),
        'enable_churn'      => get_option( 'ai_cs_enable_churn', true ),
        'enable_next_order' => get_option( 'ai_cs_enable_next_order', true ),
        'enable_campaigns'  => get_option( 'ai_cs_enable_campaigns', true ),
        'openai_model'      => get_option( 'ai_cs_openai_model', 'gpt-3.5-turbo' ),
    );
}
