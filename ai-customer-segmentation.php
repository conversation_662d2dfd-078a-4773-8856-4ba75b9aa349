<?php
/**
 * Plugin Name: AI Customer Segmentation for WooCommerce
 * Plugin URI: https://example.com/ai-customer-segmentation
 * Description: Intelligent customer segmentation using OpenAI and RFM analysis for WooCommerce stores.
 * Version: 1.0.0
 * Author: Your Name
 * Author URI: https://example.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: ai-customer-segmentation
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.3
 * Requires PHP: 7.4
 * WC requires at least: 5.0
 * WC tested up to: 8.0
 */

// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Define plugin constants
define( 'AI_CUSTOMER_SEGMENTATION_VERSION', '1.0.1' );
define( 'AI_CUSTOMER_SEGMENTATION_PLUGIN_FILE', __FILE__ );
define( 'AI_CUSTOMER_SEGMENTATION_PLUGIN_DIR', plugin_dir_path( __FILE__ ) );
define( 'AI_CUSTOMER_SEGMENTATION_PLUGIN_URL', plugin_dir_url( __FILE__ ) );
define( 'AI_CUSTOMER_SEGMENTATION_PLUGIN_BASENAME', plugin_basename( __FILE__ ) );

/**
 * Main plugin class
 */
class AI_Customer_Segmentation {

    /**
     * Single instance of the class
     *
     * @var AI_Customer_Segmentation
     */
    private static $instance = null;

    /**
     * Get single instance
     *
     * @return AI_Customer_Segmentation
     */
    public static function get_instance() {
        if ( null === self::$instance ) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        add_action( 'plugins_loaded', array( $this, 'init' ) );
        register_activation_hook( __FILE__, array( $this, 'activate' ) );
        register_deactivation_hook( __FILE__, array( $this, 'deactivate' ) );
    }

    /**
     * Initialize the plugin
     */
    public function init() {
        // Check if WooCommerce is active
        if ( ! $this->is_woocommerce_active() ) {
            add_action( 'admin_notices', array( $this, 'woocommerce_missing_notice' ) );
            return;
        }

        // Load text domain
        load_plugin_textdomain( 'ai-customer-segmentation', false, dirname( plugin_basename( __FILE__ ) ) . '/languages' );

        // Include required files
        $this->includes();

        // Initialize hooks
        $this->init_hooks();
    }

    /**
     * Include required files
     */
    private function includes() {
        // Core classes
        require_once AI_CUSTOMER_SEGMENTATION_PLUGIN_DIR . 'includes/helpers.php';
        require_once AI_CUSTOMER_SEGMENTATION_PLUGIN_DIR . 'includes/class-rfm-calculator.php';
        require_once AI_CUSTOMER_SEGMENTATION_PLUGIN_DIR . 'includes/class-openai-client.php';
        require_once AI_CUSTOMER_SEGMENTATION_PLUGIN_DIR . 'includes/class-segment-manager.php';
        require_once AI_CUSTOMER_SEGMENTATION_PLUGIN_DIR . 'includes/class-cron-handler.php';

        // Admin files
        if ( is_admin() ) {
            require_once AI_CUSTOMER_SEGMENTATION_PLUGIN_DIR . 'admin/dashboard.php';
            require_once AI_CUSTOMER_SEGMENTATION_PLUGIN_DIR . 'admin/table-view.php';
            require_once AI_CUSTOMER_SEGMENTATION_PLUGIN_DIR . 'admin/settings.php';
            require_once AI_CUSTOMER_SEGMENTATION_PLUGIN_DIR . 'admin/campaigns.php';
        }
    }

    /**
     * Initialize hooks
     */
    private function init_hooks() {
        // Admin menu
        add_action( 'admin_menu', array( $this, 'add_admin_menu' ) );
        
        // Admin scripts and styles
        add_action( 'admin_enqueue_scripts', array( $this, 'admin_enqueue_scripts' ) );

        // Initialize CRON handler
        AI_Customer_Segmentation_Cron_Handler::get_instance();

        // AJAX handlers
        add_action( 'wp_ajax_ai_cs_test_prompt', array( $this, 'handle_test_prompt' ) );
        add_action( 'wp_ajax_ai_cs_test_openai', array( $this, 'handle_test_openai' ) );
    }

    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        if ( ! current_user_can( 'manage_woocommerce' ) ) {
            return;
        }

        add_submenu_page(
            'woocommerce',
            __( 'Customer Segments', 'ai-customer-segmentation' ),
            __( 'Customer Segments', 'ai-customer-segmentation' ),
            'manage_woocommerce',
            'ai-customer-segments',
            array( $this, 'admin_page_router' )
        );
    }

    /**
     * Admin page router
     */
    public function admin_page_router() {
        $tab = isset( $_GET['tab'] ) ? sanitize_text_field( $_GET['tab'] ) : 'dashboard';

        echo '<div class="wrap">';
        echo '<h1>' . esc_html__( 'AI Customer Segmentation', 'ai-customer-segmentation' ) . '</h1>';
        
        // Tab navigation
        $this->render_tab_navigation( $tab );

        // Tab content
        switch ( $tab ) {
            case 'table-view':
                AI_Customer_Segmentation_Table_View::render();
                break;
            case 'settings':
                AI_Customer_Segmentation_Settings::render();
                break;
            case 'campaigns':
                AI_Customer_Segmentation_Campaigns::render();
                break;
            default:
                AI_Customer_Segmentation_Dashboard::render();
                break;
        }

        echo '</div>';
    }

    /**
     * Render tab navigation
     */
    private function render_tab_navigation( $current_tab ) {
        $tabs = array(
            'dashboard'  => __( 'Dashboard', 'ai-customer-segmentation' ),
            'table-view' => __( 'Table View', 'ai-customer-segmentation' ),
            'settings'   => __( 'Settings', 'ai-customer-segmentation' ),
            'campaigns'  => __( 'Campaign Ideas', 'ai-customer-segmentation' ),
        );

        echo '<nav class="nav-tab-wrapper">';
        foreach ( $tabs as $tab => $name ) {
            $class = ( $tab === $current_tab ) ? 'nav-tab nav-tab-active' : 'nav-tab';
            $url = add_query_arg( array( 'page' => 'ai-customer-segments', 'tab' => $tab ), admin_url( 'admin.php' ) );
            echo '<a href="' . esc_url( $url ) . '" class="' . esc_attr( $class ) . '">' . esc_html( $name ) . '</a>';
        }
        echo '</nav>';
    }

    /**
     * Enqueue admin scripts and styles
     */
    public function admin_enqueue_scripts( $hook ) {
        if ( 'woocommerce_page_ai-customer-segments' !== $hook ) {
            return;
        }

        // Enqueue Chart.js
        wp_enqueue_script(
            'chartjs',
            'https://cdn.jsdelivr.net/npm/chart.js',
            array(),
            '3.9.1',
            true
        );

        // Enqueue admin CSS
        wp_enqueue_style(
            'ai-customer-segmentation-admin',
            AI_CUSTOMER_SEGMENTATION_PLUGIN_URL . 'assets/css/admin.css',
            array(),
            AI_CUSTOMER_SEGMENTATION_VERSION
        );

        // Enqueue admin JS
        wp_enqueue_script(
            'ai-customer-segmentation-admin',
            AI_CUSTOMER_SEGMENTATION_PLUGIN_URL . 'assets/js/admin.js',
            array( 'jquery', 'chartjs' ),
            AI_CUSTOMER_SEGMENTATION_VERSION,
            true
        );

        // Localize script
        wp_localize_script(
            'ai-customer-segmentation-admin',
            'aiCustomerSegmentation',
            array(
                'ajaxUrl' => admin_url( 'admin-ajax.php' ),
                'nonce'   => wp_create_nonce( 'ai_customer_segmentation_nonce' ),
            )
        );
    }

    /**
     * Plugin activation
     */
    public function activate() {
        // Create database tables
        $this->create_tables();

        // Schedule CRON events
        if ( ! wp_next_scheduled( 'ai_customer_segmentation_daily_update' ) ) {
            wp_schedule_event( time(), 'daily', 'ai_customer_segmentation_daily_update' );
        }
    }

    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Clear scheduled events
        wp_clear_scheduled_hook( 'ai_customer_segmentation_daily_update' );
    }

    /**
     * Create database tables
     */
    private function create_tables() {
        global $wpdb;

        $table_name = $wpdb->prefix . 'ai_customer_segments';

        $charset_collate = $wpdb->get_charset_collate();

        $sql = "CREATE TABLE $table_name (
            id bigint(20) NOT NULL AUTO_INCREMENT,
            customer_id bigint(20) NOT NULL,
            rfm_score varchar(3) NOT NULL,
            recency_score int(1) NOT NULL,
            frequency_score int(1) NOT NULL,
            monetary_score int(1) NOT NULL,
            segment_label varchar(100) NOT NULL,
            churn_probability decimal(3,2) DEFAULT NULL,
            next_order_prediction date DEFAULT NULL,
            total_orders int(10) NOT NULL DEFAULT 0,
            total_spent decimal(10,2) NOT NULL DEFAULT 0.00,
            last_order_date datetime DEFAULT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY customer_id (customer_id),
            KEY segment_label (segment_label),
            KEY churn_probability (churn_probability)
        ) $charset_collate;";

        require_once ABSPATH . 'wp-admin/includes/upgrade.php';
        dbDelta( $sql );
    }

    /**
     * Check if WooCommerce is active
     */
    private function is_woocommerce_active() {
        return class_exists( 'WooCommerce' );
    }

    /**
     * WooCommerce missing notice
     */
    public function woocommerce_missing_notice() {
        echo '<div class="notice notice-error"><p>';
        echo esc_html__( 'AI Customer Segmentation requires WooCommerce to be installed and active.', 'ai-customer-segmentation' );
        echo '</p></div>';
    }

    /**
     * Handle test prompt AJAX request
     */
    public function handle_test_prompt() {
        // Verify nonce and permissions
        if ( ! wp_verify_nonce( $_POST['nonce'] ?? '', 'ai_customer_segmentation_nonce' ) ) {
            wp_die( 'Security check failed' );
        }

        if ( ! current_user_can( 'manage_woocommerce' ) ) {
            wp_die( 'Insufficient permissions' );
        }

        $test_data = $_POST['test_data'] ?? array();

        // Validate test data
        $customer_data = array(
            'rfm_score'             => sanitize_text_field( $test_data['rfm_score'] ?? '543' ),
            'total_spent'           => floatval( $test_data['total_spent'] ?? 1250 ),
            'total_orders'          => intval( $test_data['total_orders'] ?? 8 ),
            'days_since_last_order' => intval( $test_data['days_ago'] ?? 15 ),
        );

        $openai_client = AI_Customer_Segmentation_OpenAI_Client::get_instance();
        $segment_label = $openai_client->get_segment_label( $customer_data );

        if ( $segment_label ) {
            wp_send_json_success( array(
                'result' => $segment_label,
                'message' => __( 'Test completed successfully.', 'ai-customer-segmentation' ),
            ) );
        } else {
            wp_send_json_error( array(
                'message' => __( 'Failed to get response from OpenAI. Please check your API configuration.', 'ai-customer-segmentation' ),
            ) );
        }
    }

    /**
     * Handle test OpenAI AJAX request
     */
    public function handle_test_openai() {
        error_log( 'AI Customer Segmentation: handle_test_openai called' );

        // Verify nonce and permissions
        if ( ! wp_verify_nonce( $_POST['nonce'] ?? '', 'ai_customer_segmentation_nonce' ) ) {
            error_log( 'AI Customer Segmentation: Nonce verification failed' );
            wp_die( 'Security check failed' );
        }

        if ( ! current_user_can( 'manage_woocommerce' ) ) {
            error_log( 'AI Customer Segmentation: Insufficient permissions' );
            wp_die( 'Insufficient permissions' );
        }

        error_log( 'AI Customer Segmentation: Testing OpenAI connection' );
        $openai_client = AI_Customer_Segmentation_OpenAI_Client::get_instance();
        $test_result = $openai_client->test_api_connection();

        error_log( 'AI Customer Segmentation: Test result - ' . wp_json_encode( $test_result ) );

        if ( $test_result['success'] ) {
            wp_send_json_success( $test_result );
        } else {
            wp_send_json_error( $test_result );
        }
    }
}

// Initialize the plugin
AI_Customer_Segmentation::get_instance();
