# AI Customer Segmentation Engine for WooCommerce (OpenAI Version)

## 🧠 Overview

This plugin is a **WooCommerce extension** that enables store owners to understand, segment, and engage with their customers more intelligently using **OpenAI-powered customer profiling**. The plugin combines traditional RFM scoring (Recency, Frequency, Monetary value) with natural language processing (NLP) prompts to classify users, predict churn, and suggest retention campaigns — all without requiring any data science expertise.

---

## 🎯 Purpose

To help WooCommerce merchants:
- Automatically analyze customer purchasing behavior
- Group customers into meaningful, actionable segments
- Predict who is likely to stop buying soon (churn)
- Anticipate next purchase windows
- Generate real campaign suggestions using OpenAI
- Make better marketing decisions — quickly and with confidence

---

## 📦 Plugin Structure

### Plugin Folder
```
ai-customer-segmentation/
├── ai-customer-segmentation.php
├── includes/
│   ├── class-rfm-calculator.php
│   ├── class-openai-client.php
│   ├── class-segment-manager.php
│   ├── class-cron-handler.php
│   └── helpers.php
├── admin/
│   ├── dashboard.php
│   ├── table-view.php
│   ├── settings.php
│   ├── campaigns.php
├── assets/
│   ├── js/
│   └── css/
└── uninstall.php
```

---

## 🔧 Functional Modules

### 1. **RFM Engine**
- Calculates RFM score for every WooCommerce customer.
- Pulls all completed orders via `wc_get_orders()`.
- For each customer:
  - **Recency**: Days since last purchase
  - **Frequency**: Count of completed orders
  - **Monetary**: Total amount spent
- Converts scores into quintiles (1–5).
- Stores result in a custom DB table and/or `user_meta`.

---

### 2. **OpenAI Integration Layer**
- Powered by `class-openai-client.php`
- Uses the OpenAI API (GPT-4 or GPT-3.5).
- Sends structured prompts like:
```text
Given a customer with RFM score 543, who spent $1290 and last ordered 10 days ago,
what type of customer are they? Just return a label.
```
- Other prompt types:
  - Churn prediction
  - Next order estimation
  - Marketing campaign suggestions

---

### 3. **Customer Segmentation & Labeling**
- Final result of AI prompt is stored as:
  - Segment label (e.g., “VIP”, “At Risk”, “One-Time Buyer”)
  - Churn probability (0–1 float)
  - Next predicted order date (date string)
- Saved into:
  - Custom DB table: `wp_ai_customer_segments`
  - Optionally: `user_meta` for cross-plugin use

---

### 4. **CRON Job Handler**
- Runs on a scheduled interval (daily or weekly).
- Responsible for:
  - Recalculating RFM values
  - Re-calling OpenAI if any values changed
  - Appending new customers
  - Cleaning up invalid entries

---

## 🧑‍💻 Admin UI & User Experience

### 📊 Tab 1: **Dashboard**

#### Route:
`WooCommerce > Customer Segments > Dashboard`

#### Contents:
- Chart.js visual graphs:
  - Pie chart: % of customers by segment
  - Bar chart: LTV by segment
  - Line chart: Churn risk trend over time
- Summary cards:
  - Total Customers
  - % Churn Risk (avg)
  - Top 3 Segments by revenue
- Date range picker (7d, 30d, 90d, custom)

---

### 📋 Tab 2: **Table View**

#### Route:
`WooCommerce > Customer Segments > Table View`

#### Contents:
- Sortable, filterable table of all customers
- Columns:
  - Customer ID
  - Name
  - Segment Label
  - RFM Score
  - Total Spend
  - Orders Count
  - Churn Risk
  - Last Order
  - Next Expected Order
- Filters:
  - Segment
  - Churn risk threshold
  - Country
  - Date range
- Export as CSV

---

### ⚙️ Tab 3: **Settings**

#### Route:
`WooCommerce > Customer Segments > Settings`

#### Sections:
- 🔑 **OpenAI API Key**
  - Input field (masked)
  - Saved via `update_option()` encrypted
- ⏱️ **Refresh Frequency**
  - Daily / Weekly / Manual
- 📌 **Label Format**
  - Choose: badge, tag, or plain text
- 🧪 **Test Prompt**
  - Try a custom RFM score and preview OpenAI label
- ✅ **Toggle Features**
  - Enable churn prediction
  - Enable next order prediction
  - Enable campaign suggestions

---

### 📢 Tab 4: **Campaign Ideas**

#### Route:
`WooCommerce > Customer Segments > Campaigns`

#### Behavior:
- Sends a single OpenAI prompt:
```text
Based on these segments (30% VIP, 40% One-Time, 20% At Risk),
give me 3 WooCommerce-compatible marketing ideas with one-line subject lines.
```
- Output:
  - Campaign name
  - Goal
  - Recommended action (with button or shortcode if applicable)
- Example:
  - **"Win Back Your Lost Buyers"**
    - Target: At-Risk
    - Action: Send a 20% off coupon with AutomateWoo link

---

## 🔁 Automation Flow (CRON)

1. Runs `rfm_calculator->refresh()`
2. For any new/changed customers:
   - Call `openai_client->get_segment_label()`
   - Call `openai_client->predict_churn()`
   - Call `openai_client->predict_next_order()`
3. Save results
4. Update UI cache

---

## 🔐 Security

- Admin-only access via `manage_woocommerce`
- All API data sanitized and escaped
- All OpenAI calls rate-limited and error-handled
- API key stored with basic encryption and not shown in plain text

---

## 🧪 Developer Notes

- Use `Chart.js` or `Recharts` for admin graphs
- Use `@wordpress/scripts` for admin UI React setup
- Use `wp_schedule_event()` for cron management
- Recommend using [openai-php/client](https://github.com/openai-php/client) or raw `wp_remote_post()` for OpenAI calls

---

## 📝 Licensing and Distribution

- GPLv2 License
- Freemius / Gumroad for paid version
- Submit Lite version to WP.org, Pro version with campaign AI & churn

---

## ✅ Developer Task Checklist

- [ ] Set up plugin boilerplate and folder structure
- [ ] Build RFM calculator class
- [ ] Build OpenAI integration with API key management
- [ ] Set up DB schema installer on activation
- [ ] Build CRON task and scheduler
- [ ] Build full admin UI with settings + charts
- [ ] Implement data table for customers
- [ ] Add export + sync buttons
- [ ] Add OpenAI prompt composer and response parser
- [ ] Optimize for performance on stores with 10k+ users
